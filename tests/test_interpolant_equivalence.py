"""
Test numerical equivalence between JAX and PyTorch interpolant implementations.
"""

import torch
import numpy as np
import pytest
import sys
import os

# Add the parent directory to the path to import torch_interpolants
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from torch_interpolants.interpolant import Interpolant, setup_interpolant

# Try to import JAX components for comparison
try:
    import jax
    import jax.numpy as jnp
    sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'jax-interpolants', 'py'))
    from common.interpolant import Interpolant as JAXInterpolant, setup_interpolant as jax_setup_interpolant
    from ml_collections import config_dict
    JAX_AVAILABLE = True
except ImportError:
    JAX_AVAILABLE = False
    print("JAX not available, skipping JAX comparison tests")


class TestConfig:
    """Mock config class for testing."""
    def __init__(self, interp_type="linear", tmax=1.0, tf=1.0):
        self.problem = type('obj', (object,), {
            'interp_type': interp_type,
            'tmax': tmax,
            'tf': tf
        })


@pytest.mark.parametrize("seed", range(10))
@pytest.mark.parametrize("interp_type", ["linear", "trig"])
def test_interpolant_consistency(seed, interp_type):
    """Test that PyTorch interpolant produces consistent results across seeds."""
    torch.manual_seed(seed)
    np.random.seed(seed)
    
    # Create config
    config = TestConfig(interp_type=interp_type)
    
    # Setup interpolant
    interpolant = setup_interpolant(config)
    
    # Test data
    batch_size = 32
    dim = 64
    x0 = torch.randn(batch_size, dim)
    x1 = torch.randn(batch_size, dim)
    t = torch.rand(batch_size)
    
    # Test interpolant calculation
    It = interpolant.calc_It(t[0], x0[0], x1[0])
    It_dot = interpolant.calc_It_dot(t[0], x0[0], x1[0])
    
    # Test batch calculation
    It_batch = interpolant.batch_calc_It(t, x0, x1)
    It_dot_batch = interpolant.batch_calc_It_dot(t, x0, x1)
    
    # Check shapes
    assert It.shape == x0[0].shape
    assert It_dot.shape == x0[0].shape
    assert It_batch.shape == x0.shape
    assert It_dot_batch.shape == x0.shape
    
    # Check that batch and single calculations are consistent
    for i in range(min(5, batch_size)):  # Test first 5 samples
        It_single = interpolant.calc_It(t[i], x0[i], x1[i])
        It_dot_single = interpolant.calc_It_dot(t[i], x0[i], x1[i])
        
        torch.testing.assert_close(It_single, It_batch[i], rtol=1e-5, atol=1e-6)
        torch.testing.assert_close(It_dot_single, It_dot_batch[i], rtol=1e-5, atol=1e-6)


@pytest.mark.parametrize("target_type", ["velocity", "score", "noise", "denoiser"])
def test_target_calculation(target_type):
    """Test target calculation for different target types."""
    torch.manual_seed(42)
    
    config = TestConfig(interp_type="linear")
    interpolant = setup_interpolant(config)
    
    batch_size = 16
    dim = 32
    x0 = torch.randn(batch_size, dim)
    x1 = torch.randn(batch_size, dim)
    t = torch.rand(batch_size)
    
    # Test single target calculation
    target = interpolant.calc_target(t[0], x0[0], x1[0], target_type)
    assert target.shape == x0[0].shape
    
    # Test batch target calculation
    target_batch = interpolant.batch_calc_target(t, x0, x1, target_type)
    assert target_batch.shape == x0.shape
    
    # Verify specific target types
    if target_type == "velocity":
        expected = interpolant.calc_It_dot(t[0], x0[0], x1[0])
        torch.testing.assert_close(target, expected, rtol=1e-5, atol=1e-6)
    elif target_type == "noise":
        torch.testing.assert_close(target, x0[0], rtol=1e-5, atol=1e-6)
    elif target_type == "denoiser":
        torch.testing.assert_close(target, x1[0], rtol=1e-5, atol=1e-6)


@pytest.mark.skipif(not JAX_AVAILABLE, reason="JAX not available")
@pytest.mark.parametrize("seed", range(5))
@pytest.mark.parametrize("interp_type", ["linear", "trig"])
def test_jax_pytorch_equivalence(seed, interp_type):
    """Test numerical equivalence between JAX and PyTorch implementations."""
    # Set seeds
    torch.manual_seed(seed)
    np.random.seed(seed)
    
    # Create configs
    torch_config = TestConfig(interp_type=interp_type)
    
    jax_config = config_dict.ConfigDict()
    jax_config.problem = config_dict.ConfigDict()
    jax_config.problem.interp_type = interp_type
    jax_config.problem.tmax = 1.0
    jax_config.problem.tf = 1.0
    
    # Setup interpolants
    torch_interpolant = setup_interpolant(torch_config)
    jax_interpolant = jax_setup_interpolant(jax_config)
    
    # Test data
    batch_size = 16
    dim = 32
    
    # Generate same random data for both
    np.random.seed(seed)
    x0_np = np.random.randn(batch_size, dim).astype(np.float32)
    x1_np = np.random.randn(batch_size, dim).astype(np.float32)
    t_np = np.random.rand(batch_size).astype(np.float32)
    
    # Convert to respective tensor types
    x0_torch = torch.from_numpy(x0_np)
    x1_torch = torch.from_numpy(x1_np)
    t_torch = torch.from_numpy(t_np)
    
    x0_jax = jnp.array(x0_np)
    x1_jax = jnp.array(x1_np)
    t_jax = jnp.array(t_np)
    
    # Test single sample calculations
    for i in range(min(3, batch_size)):
        # PyTorch calculations
        It_torch = torch_interpolant.calc_It(t_torch[i], x0_torch[i], x1_torch[i])
        It_dot_torch = torch_interpolant.calc_It_dot(t_torch[i], x0_torch[i], x1_torch[i])
        
        # JAX calculations
        It_jax = jax_interpolant.calc_It(t_jax[i], x0_jax[i], x1_jax[i])
        It_dot_jax = jax_interpolant.calc_It_dot(t_jax[i], x0_jax[i], x1_jax[i])
        
        # Convert JAX results to numpy for comparison
        It_jax_np = np.array(It_jax)
        It_dot_jax_np = np.array(It_dot_jax)
        
        # Compare results
        np.testing.assert_allclose(
            It_torch.numpy(), It_jax_np, rtol=1e-4, atol=1e-6,
            err_msg=f"Interpolant mismatch for seed {seed}, type {interp_type}, sample {i}"
        )
        np.testing.assert_allclose(
            It_dot_torch.numpy(), It_dot_jax_np, rtol=1e-4, atol=1e-6,
            err_msg=f"Interpolant derivative mismatch for seed {seed}, type {interp_type}, sample {i}"
        )
    
    # Test batch calculations
    It_batch_torch = torch_interpolant.batch_calc_It(t_torch, x0_torch, x1_torch)
    It_dot_batch_torch = torch_interpolant.batch_calc_It_dot(t_torch, x0_torch, x1_torch)
    
    It_batch_jax = jax_interpolant.batch_calc_It(t_jax, x0_jax, x1_jax)
    It_dot_batch_jax = jax_interpolant.batch_calc_It_dot(t_jax, x0_jax, x1_jax)
    
    # Convert JAX results to numpy
    It_batch_jax_np = np.array(It_batch_jax)
    It_dot_batch_jax_np = np.array(It_dot_batch_jax)
    
    # Compare batch results
    np.testing.assert_allclose(
        It_batch_torch.numpy(), It_batch_jax_np, rtol=1e-4, atol=1e-6,
        err_msg=f"Batch interpolant mismatch for seed {seed}, type {interp_type}"
    )
    np.testing.assert_allclose(
        It_dot_batch_torch.numpy(), It_dot_batch_jax_np, rtol=1e-4, atol=1e-6,
        err_msg=f"Batch interpolant derivative mismatch for seed {seed}, type {interp_type}"
    )


def test_interpolant_properties():
    """Test mathematical properties of interpolants."""
    torch.manual_seed(42)
    
    config = TestConfig(interp_type="linear")
    interpolant = setup_interpolant(config)
    
    dim = 32
    x0 = torch.randn(dim)
    x1 = torch.randn(dim)
    
    # Test boundary conditions for linear interpolant
    # At t=0, should return x0
    It_0 = interpolant.calc_It(torch.tensor(0.0), x0, x1)
    torch.testing.assert_close(It_0, x0, rtol=1e-5, atol=1e-6)
    
    # At t=1, should return x1
    It_1 = interpolant.calc_It(torch.tensor(1.0), x0, x1)
    torch.testing.assert_close(It_1, x1, rtol=1e-5, atol=1e-6)
    
    # Test that derivative is constant for linear interpolant
    t_vals = torch.linspace(0.1, 0.9, 10)
    derivatives = []
    for t in t_vals:
        It_dot = interpolant.calc_It_dot(t, x0, x1)
        derivatives.append(It_dot)
    
    # All derivatives should be the same for linear interpolant
    for i in range(1, len(derivatives)):
        torch.testing.assert_close(derivatives[0], derivatives[i], rtol=1e-5, atol=1e-6)


def test_trigonometric_interpolant():
    """Test trigonometric interpolant properties."""
    torch.manual_seed(42)
    
    config = TestConfig(interp_type="trig")
    interpolant = setup_interpolant(config)
    
    dim = 32
    x0 = torch.randn(dim)
    x1 = torch.randn(dim)
    
    # Test boundary conditions
    # At t=0, should return x0
    It_0 = interpolant.calc_It(torch.tensor(0.0), x0, x1)
    torch.testing.assert_close(It_0, x0, rtol=1e-5, atol=1e-6)
    
    # At t=1, should return x1
    It_1 = interpolant.calc_It(torch.tensor(1.0), x0, x1)
    torch.testing.assert_close(It_1, x1, rtol=1e-5, atol=1e-6)
    
    # Test smoothness - derivative should be continuous
    t_vals = torch.linspace(0.01, 0.99, 100)
    for t in t_vals:
        It_dot = interpolant.calc_It_dot(t, x0, x1)
        assert torch.isfinite(It_dot).all(), f"Non-finite derivative at t={t}"


if __name__ == "__main__":
    # Run tests manually if called directly
    print("Running interpolant equivalence tests...")
    
    # Test basic functionality
    test_interpolant_consistency(42, "linear")
    test_target_calculation("velocity")
    test_interpolant_properties()
    test_trigonometric_interpolant()
    
    print("Basic tests passed!")
    
    # Test JAX equivalence if available
    if JAX_AVAILABLE:
        print("Testing JAX-PyTorch equivalence...")
        test_jax_pytorch_equivalence(42, "linear")
        test_jax_pytorch_equivalence(42, "trig")
        print("JAX equivalence tests passed!")
    else:
        print("JAX not available, skipping equivalence tests")
    
    print("All tests completed successfully!")
