"""
CIFAR-10 training experiment for PyTorch interpolants.
"""

import torch
import torch.nn as nn
import argparse
import logging
import os
from typing import Dict, Any

from ..interpolant import setup_interpolant
from ..networks.edm2_net import PrecondUNet
from ..training.trainer import InterpolantTrainer
from ..datasets.loaders import get_cifar10_dataloader
from ..configs.cifar10 import (
    get_cifar10_config, get_cifar10_debug_config, 
    get_cifar10_conditional_config, get_cifar10_large_config
)


def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('cifar10_training.log')
        ]
    )


def create_model(config: Dict[str, Any], device: torch.device) -> nn.Module:
    """Create the model based on configuration."""
    network_config = config['network']
    
    model = PrecondUNet(
        img_resolution=network_config['img_resolution'],
        img_channels=network_config['img_channels'],
        label_dim=network_config['label_dim'],
        sigma_data=network_config.get('sigma_data', 0.5),
        logvar_channels=network_config['logvar_channels'],
        use_bfloat16=network_config['use_bfloat16'],
        **network_config['unet_kwargs']
    )
    
    return model.to(device)


def train_cifar10(
    config_type: str = "default",
    dataset_location: str = "./data",
    output_folder: str = "./outputs",
    wandb_entity: str = None,
    use_wandb: bool = False,
    device: str = "auto",
    resume_from: str = None,
) -> None:
    """Train CIFAR-10 interpolant model."""
    
    # Setup device
    if device == "auto":
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device)
    
    print(f"Using device: {device}")
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # Get configuration
    if config_type == "debug":
        config = get_cifar10_debug_config(dataset_location, output_folder, wandb_entity)
    elif config_type == "conditional":
        config = get_cifar10_conditional_config(dataset_location, output_folder, wandb_entity)
    elif config_type == "large":
        config = get_cifar10_large_config(dataset_location, output_folder, wandb_entity)
    else:
        config = get_cifar10_config(dataset_location, output_folder, wandb_entity)
    
    logger.info(f"Using config type: {config_type}")
    logger.info(f"Training steps: {config['optimization']['total_steps']}")
    logger.info(f"Batch size: {config['optimization']['bs']}")
    
    # Create output directory
    os.makedirs(output_folder, exist_ok=True)
    
    # Setup interpolant
    interpolant = setup_interpolant(config)
    logger.info(f"Using interpolant type: {config['problem']['interp_type']}")
    
    # Create model
    model = create_model(config, device)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"Total parameters: {total_params:,}")
    logger.info(f"Trainable parameters: {trainable_params:,}")
    
    # Create dataloader
    train_dataloader = get_cifar10_dataloader(
        batch_size=config['optimization']['bs'],
        train=True,
        download=True,
        data_root=dataset_location,
        conditional=config['training']['conditional'],
        augment=True,
    )
    
    logger.info(f"Training dataset size: {len(train_dataloader.dataset)}")
    
    # Create trainer
    trainer = InterpolantTrainer(
        model=model,
        interpolant=interpolant,
        config=config,
        device=device,
        use_wandb=use_wandb,
    )
    
    # Resume from checkpoint if specified
    if resume_from:
        logger.info(f"Resuming from checkpoint: {resume_from}")
        from ..training.utils import load_checkpoint
        checkpoint = load_checkpoint(
            resume_from, model, trainer.optimizer, trainer.scheduler, trainer.ema, device
        )
        trainer.step = checkpoint['step']
        trainer.epoch = checkpoint.get('epoch', 0)
        logger.info(f"Resumed from step {trainer.step}")
    
    # Start training
    logger.info("Starting training...")
    trainer.train(train_dataloader)
    
    logger.info("Training completed!")
    
    # Generate some samples
    logger.info("Generating samples...")
    samples = trainer.sample(num_samples=25, num_steps=50)
    
    # Save samples
    import torchvision.utils as vutils
    sample_path = os.path.join(output_folder, 'final_samples.png')
    vutils.save_image(
        samples,
        sample_path,
        nrow=5,
        normalize=True,
        value_range=(-1, 1)
    )
    logger.info(f"Samples saved to: {sample_path}")


def main():
    """Main function for command line interface."""
    parser = argparse.ArgumentParser(description="Train CIFAR-10 interpolant model")
    parser.add_argument("--config-type", type=str, default="default",
                       choices=["default", "debug", "conditional", "large"],
                       help="Configuration type")
    parser.add_argument("--dataset-location", type=str, default="./data",
                       help="Dataset location")
    parser.add_argument("--output-folder", type=str, default="./outputs",
                       help="Output folder")
    parser.add_argument("--wandb-entity", type=str, default=None,
                       help="Weights & Biases entity")
    parser.add_argument("--use-wandb", action="store_true",
                       help="Use Weights & Biases logging")
    parser.add_argument("--device", type=str, default="auto",
                       help="Device to use (auto, cpu, cuda)")
    parser.add_argument("--resume-from", type=str, default=None,
                       help="Resume from checkpoint")
    
    args = parser.parse_args()
    
    train_cifar10(
        config_type=args.config_type,
        dataset_location=args.dataset_location,
        output_folder=args.output_folder,
        wandb_entity=args.wandb_entity,
        use_wandb=args.use_wandb,
        device=args.device,
        resume_from=args.resume_from,
    )


if __name__ == "__main__":
    main()
