"""
FID evaluation for PyTorch interpolants.
"""

import torch
import torch.nn as nn
import numpy as np
import argparse
import logging
import os
from typing import Dict, Any, Optional, Tuple
from tqdm import tqdm

try:
    from torch_fidelity import calculate_metrics
    TORCH_FIDELITY_AVAILABLE = True
except ImportError:
    TORCH_FIDELITY_AVAILABLE = False
    print("Warning: torch-fidelity not available. Install with: pip install torch-fidelity")

try:
    from cleanfid import fid
    CLEANFID_AVAILABLE = True
except ImportError:
    CLEANFID_AVAILABLE = False
    print("Warning: clean-fid not available. Install with: pip install clean-fid")

from ..interpolant import setup_interpolant
from ..networks.edm2_net import PrecondUNet
from ..training.trainer import InterpolantTrainer
from ..datasets.loaders import get_mnist_dataloader, get_cifar10_dataloader
from ..configs.mnist import get_mnist_config
from ..configs.cifar10 import get_cifar10_config


def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('fid_evaluation.log')
        ]
    )


def load_model_from_checkpoint(
    checkpoint_path: str,
    config: Dict[str, Any],
    device: torch.device
) -> nn.Module:
    """Load model from checkpoint."""
    from ..training.utils import load_checkpoint
    
    # Create model
    network_config = config['network']
    model = PrecondUNet(
        img_resolution=network_config['img_resolution'],
        img_channels=network_config['img_channels'],
        label_dim=network_config['label_dim'],
        sigma_data=network_config['sigma_data'],
        logvar_channels=network_config['logvar_channels'],
        use_bfloat16=network_config['use_bfloat16'],
        **network_config['unet_kwargs']
    ).to(device)
    
    # Load checkpoint
    checkpoint = load_checkpoint(checkpoint_path, model, device=device)
    
    return model


def generate_samples(
    model: nn.Module,
    interpolant,
    config: Dict[str, Any],
    num_samples: int,
    num_steps: int = 50,
    batch_size: int = 100,
    device: torch.device = None,
    method: str = "heun",
) -> torch.Tensor:
    """Generate samples from the model."""
    from ..samplers import batch_sample
    
    model.eval()
    all_samples = []
    
    def apply_velocity(variables, t, x, label, train=False):
        return model(t, x, label, train=train)
    
    num_batches = (num_samples + batch_size - 1) // batch_size
    
    with torch.no_grad():
        for i in tqdm(range(num_batches), desc="Generating samples"):
            current_batch_size = min(batch_size, num_samples - i * batch_size)
            
            # Sample from base distribution
            x0 = torch.randn(
                current_batch_size, 
                *config['problem']['image_dims'], 
                device=device
            )
            
            # Create dummy labels
            labels = torch.zeros(current_batch_size, dtype=torch.long, device=device)
            
            # Generate samples
            samples = batch_sample(
                apply_velocity=apply_velocity,
                variables={},
                x0s=x0,
                N=num_steps,
                labels=labels,
                method=method,
                device=device,
            )
            
            all_samples.append(samples.cpu())
    
    return torch.cat(all_samples, dim=0)[:num_samples]


def save_samples_as_images(
    samples: torch.Tensor,
    output_dir: str,
    prefix: str = "sample"
) -> str:
    """Save samples as individual image files."""
    os.makedirs(output_dir, exist_ok=True)
    
    # Denormalize samples from [-1, 1] to [0, 1]
    samples = (samples + 1) / 2
    samples = torch.clamp(samples, 0, 1)
    
    # Convert to PIL images and save
    import torchvision.transforms as transforms
    to_pil = transforms.ToPILImage()
    
    for i, sample in enumerate(samples):
        img = to_pil(sample)
        img_path = os.path.join(output_dir, f"{prefix}_{i:06d}.png")
        img.save(img_path)
    
    return output_dir


def calculate_fid_torch_fidelity(
    generated_samples: torch.Tensor,
    real_dataset_name: str,
    device: torch.device,
    batch_size: int = 50,
) -> float:
    """Calculate FID using torch-fidelity."""
    if not TORCH_FIDELITY_AVAILABLE:
        raise ImportError("torch-fidelity is required for FID calculation")
    
    # Save generated samples to temporary directory
    import tempfile
    with tempfile.TemporaryDirectory() as temp_dir:
        generated_dir = os.path.join(temp_dir, "generated")
        save_samples_as_images(generated_samples, generated_dir)
        
        # Calculate metrics
        metrics = calculate_metrics(
            input1=generated_dir,
            input2=real_dataset_name,
            cuda=device.type == 'cuda',
            batch_size=batch_size,
            fid=True,
            verbose=False,
        )
        
        return metrics['frechet_inception_distance']


def calculate_fid_clean_fid(
    generated_samples: torch.Tensor,
    real_dataset_name: str,
    device: torch.device,
) -> float:
    """Calculate FID using clean-fid."""
    if not CLEANFID_AVAILABLE:
        raise ImportError("clean-fid is required for FID calculation")
    
    # Save generated samples to temporary directory
    import tempfile
    with tempfile.TemporaryDirectory() as temp_dir:
        generated_dir = os.path.join(temp_dir, "generated")
        save_samples_as_images(generated_samples, generated_dir)
        
        # Calculate FID
        fid_score = fid.compute_fid(
            generated_dir,
            dataset_name=real_dataset_name,
            mode="clean",
            device=device,
        )
        
        return fid_score


def evaluate_fid(
    checkpoint_path: str,
    dataset: str = "cifar10",
    num_samples: int = 50000,
    num_steps: int = 50,
    batch_size: int = 100,
    method: str = "heun",
    output_dir: str = "./fid_evaluation",
    device: str = "auto",
    fid_backend: str = "torch_fidelity",
    save_samples: bool = False,
) -> Dict[str, float]:
    """Evaluate FID score for a trained model."""
    
    # Setup device
    if device == "auto":
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device)
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info(f"Evaluating FID for {dataset} with {num_samples} samples")
    logger.info(f"Using device: {device}")
    logger.info(f"Checkpoint: {checkpoint_path}")
    
    # Get configuration
    if dataset == "mnist":
        config = get_mnist_config()
        real_dataset_name = "mnist"
    elif dataset == "cifar10":
        config = get_cifar10_config()
        real_dataset_name = "cifar10"
    else:
        raise ValueError(f"Unknown dataset: {dataset}")
    
    # Setup interpolant
    interpolant = setup_interpolant(config)
    
    # Load model
    logger.info("Loading model from checkpoint...")
    model = load_model_from_checkpoint(checkpoint_path, config, device)
    
    # Generate samples
    logger.info(f"Generating {num_samples} samples...")
    samples = generate_samples(
        model=model,
        interpolant=interpolant,
        config=config,
        num_samples=num_samples,
        num_steps=num_steps,
        batch_size=batch_size,
        device=device,
        method=method,
    )
    
    # Save samples if requested
    if save_samples:
        os.makedirs(output_dir, exist_ok=True)
        samples_dir = os.path.join(output_dir, "generated_samples")
        save_samples_as_images(samples, samples_dir)
        logger.info(f"Samples saved to: {samples_dir}")
        
        # Also save a grid of samples
        import torchvision.utils as vutils
        grid_path = os.path.join(output_dir, "samples_grid.png")
        vutils.save_image(
            samples[:25],
            grid_path,
            nrow=5,
            normalize=True,
            value_range=(-1, 1)
        )
        logger.info(f"Sample grid saved to: {grid_path}")
    
    # Calculate FID
    logger.info("Calculating FID score...")
    if fid_backend == "torch_fidelity":
        fid_score = calculate_fid_torch_fidelity(samples, real_dataset_name, device)
    elif fid_backend == "clean_fid":
        fid_score = calculate_fid_clean_fid(samples, real_dataset_name, device)
    else:
        raise ValueError(f"Unknown FID backend: {fid_backend}")
    
    logger.info(f"FID Score: {fid_score:.4f}")
    
    # Save results
    results = {
        'fid_score': fid_score,
        'num_samples': num_samples,
        'num_steps': num_steps,
        'method': method,
        'dataset': dataset,
        'checkpoint_path': checkpoint_path,
    }
    
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        results_path = os.path.join(output_dir, "fid_results.json")
        import json
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2)
        logger.info(f"Results saved to: {results_path}")
    
    return results


def main():
    """Main function for command line interface."""
    parser = argparse.ArgumentParser(description="Evaluate FID score for interpolant model")
    parser.add_argument("checkpoint_path", type=str, help="Path to model checkpoint")
    parser.add_argument("--dataset", type=str, default="cifar10",
                       choices=["mnist", "cifar10"], help="Dataset to evaluate on")
    parser.add_argument("--num-samples", type=int, default=50000,
                       help="Number of samples to generate")
    parser.add_argument("--num-steps", type=int, default=50,
                       help="Number of sampling steps")
    parser.add_argument("--batch-size", type=int, default=100,
                       help="Batch size for generation")
    parser.add_argument("--method", type=str, default="heun",
                       choices=["euler", "heun"], help="Sampling method")
    parser.add_argument("--output-dir", type=str, default="./fid_evaluation",
                       help="Output directory")
    parser.add_argument("--device", type=str, default="auto",
                       help="Device to use (auto, cpu, cuda)")
    parser.add_argument("--fid-backend", type=str, default="torch_fidelity",
                       choices=["torch_fidelity", "clean_fid"], help="FID calculation backend")
    parser.add_argument("--save-samples", action="store_true",
                       help="Save generated samples")
    
    args = parser.parse_args()
    
    results = evaluate_fid(
        checkpoint_path=args.checkpoint_path,
        dataset=args.dataset,
        num_samples=args.num_samples,
        num_steps=args.num_steps,
        batch_size=args.batch_size,
        method=args.method,
        output_dir=args.output_dir,
        device=args.device,
        fid_backend=args.fid_backend,
        save_samples=args.save_samples,
    )
    
    print(f"\nFID Score: {results['fid_score']:.4f}")


if __name__ == "__main__":
    main()
