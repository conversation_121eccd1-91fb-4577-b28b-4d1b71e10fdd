"""
PyTorch implementation of loss functions for stochastic interpolants.
"""
import torch
import torch.nn as nn
from typing import Callable
from .interpolant import Interpolant 


def empirical_loss(
    model: nn.Module,
    x0_batch: torch.Tensor,
    x1_batch: torch.Tensor,
    label_batch: torch.Tensor,
    t_batch: torch.Tensor,
    *,
    interp: Interpolant,
    loss_type: str,
) -> torch.Tensor:
    """
    Computes the vectorized empirical loss.
    This version calls the corrected, batch-aware methods of the Interpolant class.
    """
    # --- MODIFIED: Call the base methods directly ---
    It_batch = interp.calc_It(t_batch, x0_batch, x1_batch)
    target_batch = interp.calc_target(t_batch, x0_batch, x1_batch, loss_type)

    # Get model predictions for the batch
    bt_batch = model(t_batch, It_batch, label_batch)

    # Compute the squared error loss for each item in the batch
    loss_per_item = torch.sum((bt_batch - target_batch) ** 2, dim=list(range(1, bt_batch.dim())))

    # Apply the time-dependent weighting if the model has it
    if hasattr(model, 'calc_weight'):
        weight_batch = model.calc_weight(t_batch)
        loss_per_item = torch.exp(-weight_batch) * loss_per_item + weight_batch

    # Return the mean of the losses over the batch
    return loss_per_item.mean()

def setup_loss(
    config, # Can be a dict or an object
    model: nn.Module,
    interp: Interpolant,
) -> Callable:
    """
    Sets up and returns the configured loss function for training.
    """
    # Extract loss_type from the configuration
    loss_type = config['training']['loss_type'] if isinstance(config, dict) else config.training.loss_type

    # Create a partial function with the fixed arguments
    def loss_fn(
        model: nn.Module,
        x0_batch: torch.Tensor,
        x1_batch: torch.Tensor,
        label_batch: torch.Tensor,
        t_batch: torch.Tensor,
    ) -> torch.Tensor:
        """This is the final loss function to be passed to the optimizer."""
        return empirical_loss(
            model=model,
            x0_batch=x0_batch,
            x1_batch=x1_batch,
            label_batch=label_batch,
            t_batch=t_batch,
            interp=interp,
            loss_type=loss_type,
        )

    return loss_fn