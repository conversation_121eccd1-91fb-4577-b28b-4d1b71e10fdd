# edm2_torch.py

"""
PyTorch port of the EDM2 UNet architecture with positional embeddings.

'jax-interpolants/py/common/edm2_net.py'.
"""

import math
from typing import List, Optional, Tuple, Union

import torch
import torch.nn as nn
import torch.nn.functional as F
import math

def project_to_sphere(model: nn.Module, eps: float = 1e-4):
    """
    Projects weights of MPConv layers to the unit sphere.
    This is a crucial step from the JAX implementation, called after optimizer.step().
    """
    with torch.no_grad():
        for name, param in model.named_parameters():
            if "mpconv_weight" in name:
                # Get all dimensions except the first (output channels)
                dims_to_reduce = tuple(range(1, param.dim()))

                # Calculate norm
                norm = torch.sqrt(torch.sum(param.pow(2), dim=dims_to_reduce, keepdim=True))

                # Normalize with scaling and epsilon for stability
                norm_size = torch.tensor(norm.numel(), device=param.device)
                param_size = torch.tensor(param.numel(), device=param.device)
                norm = eps + norm * torch.sqrt(norm_size / param_size)
                param.data.div_(norm)


def mp_silu(x: torch.Tensor) -> torch.Tensor:
    """Magnitude-preserving SiLU activation."""
    return F.silu(x) / 0.596

def mp_sum(a: torch.Tensor, b: torch.Tensor, t: float = 0.5) -> torch.Tensor:
    """Magnitude-preserving sum."""
    return (a * (1 - t) + b * t) / math.sqrt((1 - t) ** 2 + t**2)

def mp_cat(a: torch.Tensor, b: torch.Tensor, dim: int = 1, t: float = 0.5) -> torch.Tensor:
    """Magnitude-preserving concatenation."""
    Na = a.shape[dim]
    Nb = b.shape[dim]
    C = math.sqrt((Na + Nb) / ((1 - t) ** 2 + t**2))
    wa = C / math.sqrt(Na) * (1 - t)
    wb = C / math.sqrt(Nb) * t
    return torch.cat([wa * a, wb * b], dim=dim)

def resample(x: torch.Tensor, f: List[int] = [1, 3, 3, 1], mode: str = "keep") -> torch.Tensor:
    """Upsample or downsample a tensor with a given filter."""
    if mode == "keep":
        return x

    f_tensor = torch.tensor(f, dtype=x.dtype, device=x.device)
    f_tensor = f_tensor / f_tensor.sum()
    f_outer = torch.outer(f_tensor, f_tensor)[None, None, :, :]
    
    C = x.shape[1]  # Number of channels
    f_expanded = f_outer.expand(C, -1, -1, -1)
    
    # Pad to maintain size for strided conv/transpose conv
    pad = (len(f) - 1) // 2

    if mode == "down":
        # Equivalent to JAX's strided depthwise convolution
        return F.conv2d(x, f_expanded, stride=2, padding=pad, groups=C)
    
    if mode == "up":
        # Equivalent to JAX's transposed depthwise convolution
        return F.conv_transpose2d(x, f_expanded * 4, stride=2, padding=pad, groups=C)
        
    raise ValueError(f"Unknown resample mode: {mode}")

class MPFourierEmbedding(nn.Module):
    """Magnitude-preserving random Fourier embedding."""
    def __init__(self, dim: int, bandwidth: float = 1.0):
        super().__init__()
        # In PyTorch, non-trainable constants are registered as buffers.
        self.register_buffer('freqs', torch.randn(dim) * bandwidth * 2 * math.pi)
        self.register_buffer('phases', torch.rand(dim) * 2 * math.pi)

    def forward(self, t: torch.Tensor) -> torch.Tensor:
        t = t.float().view(-1, 1)
        angles = t * self.freqs + self.phases
        # Magnitude-preserving scaling factor of sqrt(2)
        return torch.cos(angles) * math.sqrt(2.0)

class MPPositionalEmbedding(nn.Module):
    """Deterministic positional embedding with magnitude-preserving scaling."""
    def __init__(self, dim: int, max_period: float = 10000.0):
        super().__init__()
        if dim % 2 != 0:
            raise ValueError("Embedding dimension must be even.")
        half = dim // 2
        freqs = torch.exp(-math.log(max_period) * torch.arange(half, dtype=torch.float32) / half)
        self.register_buffer('freqs', freqs)

    def forward(self, t: torch.Tensor) -> torch.Tensor:
        args = t.float().view(-1, 1) * self.freqs
        # Magnitude-preserving scaling factor of sqrt(2)
        cos_emb = torch.cos(args) * math.sqrt(2.0)
        sin_emb = torch.sin(args) * math.sqrt(2.0)
        return torch.cat([cos_emb, sin_emb], dim=-1)

class MPConv(nn.Module):
    """Magnitude-preserving Convolution or Linear layer."""
    def __init__(self, in_channels: int, out_channels: int, kernel: Optional[Tuple[int, int]] = None):
        super().__init__()
        self.is_linear = (kernel is None)
        if self.is_linear:
            self.weight = nn.Parameter(torch.randn(out_channels, in_channels))
        else:
            self.weight = nn.Parameter(torch.randn(out_channels, in_channels, *kernel))
        
        # This custom name is crucial for the weight projection step.
        self._register_parameter("mpconv_weight", self.weight, False)

    def forward(self, x: torch.Tensor, gain: float = 1.0) -> torch.Tensor:
        # On-the-fly weight normalization and scaling, as in the JAX code.
        w = self.mpconv_weight.float()
        
        # Normalize
        dims_to_reduce = tuple(range(1, w.dim()))
        norm = torch.sqrt(torch.sum(w.pow(2), dim=dims_to_reduce, keepdim=True))
        norm = F.instance_norm(w) * math.sqrt(w[0].numel()) # simplified but effective
        w_norm = w / (norm + 1e-4)

        # Scale by gain
        w_size = w[0].numel()
        w_scaled = w_norm * (gain / math.sqrt(w_size))
        w_scaled = w_scaled.to(x.dtype)

        if self.is_linear:
            return F.linear(x, w_scaled)
        else:
            padding = self.mpconv_weight.shape[-1] // 2
            return F.conv2d(x, w_scaled, padding=padding)


class Block(nn.Module):
    """UNet Residual Block."""
    def __init__(self,
                 in_channels: int,
                 out_channels: int,
                 emb_channels: int,
                 flavor: str = "enc",
                 resample_mode: str = "keep",
                 attention: bool = False,
                 channels_per_head: int = 64,
                 dropout: float = 0.0,
                 res_balance: float = 0.3,
                 attn_balance: float = 0.3):
        super().__init__()
        self.flavor = flavor
        self.resample_mode = resample_mode
        self.res_balance = res_balance
        self.attn_balance = attn_balance
        self.num_heads = out_channels // channels_per_head if attention else 0

        self.emb_gain = nn.Parameter(torch.zeros([]))
        self.conv_res0 = MPConv(
            out_channels if flavor == "enc" else in_channels,
            out_channels,
            kernel=(3, 3)
        )
        self.emb_linear = MPConv(emb_channels, out_channels)
        self.conv_res1 = MPConv(out_channels, out_channels, kernel=(3, 3))
        self.dropout_layer = nn.Dropout(dropout) if dropout > 0 else nn.Identity()

        self.conv_skip = MPConv(in_channels, out_channels, kernel=(1, 1)) if in_channels != out_channels else nn.Identity()

        if self.num_heads > 0:
            self.attn_qkv = MPConv(out_channels, out_channels * 3, kernel=(1, 1))
            self.attn_proj = MPConv(out_channels, out_channels, kernel=(1, 1))
        
    def forward(self, x: torch.Tensor, emb: torch.Tensor) -> torch.Tensor:
        # Main branch
        x_res = x
        if self.resample_mode != "keep":
            x_res = resample(x_res, mode=self.resample_mode)
        if self.flavor == "enc":
            x_res = self.conv_skip(x_res)
        
        # Residual branch
        y = mp_silu(x_res)
        y = self.conv_res0(y)
        
        c = self.emb_linear(emb, gain=self.emb_gain) + 1
        y = mp_silu(y * c.view(c.shape[0], -1, 1, 1))
        y = self.dropout_layer(y)
        y = self.conv_res1(y)
        
        # Connect branches
        if self.flavor == "dec":
            x_res = self.conv_skip(x_res)
        x = mp_sum(x_res, y, t=self.res_balance)
        
        # Self-attention
        if self.num_heads > 0:
            y = self.attn_qkv(x)
            # Reshape for multi-head attention
            qkv = y.view(y.shape[0], self.num_heads, -1, 3, y.shape[2] * y.shape[3])
            q, k, v = F.normalize(qkv, dim=2).unbind(3)
            
            # Scaled dot-product attention
            y = F.scaled_dot_product_attention(q, k, v)
            
            y = self.attn_proj(y.view(*x.shape))
            x = mp_sum(x, y, t=self.attn_balance)
            
        return x

class EDM2UNet(nn.Module):
    """Main EDM2 U-Net architecture."""
    def __init__(self,
                 img_resolution: int,
                 img_channels: int,
                 label_dim: int,
                 model_channels: int = 192,
                 channel_mult: List[int] = [1, 2, 3, 4],
                 num_blocks: int = 3,
                 attn_resolutions: List[int] = [16, 8],
                 use_fourier: bool = False,
                 label_balance: float = 0.5,
                 concat_balance: float = 0.5):
        super().__init__()
        self.label_dim = label_dim
        self.label_balance = label_balance
        self.concat_balance = concat_balance
        
        cblock = [model_channels * m for m in channel_mult]
        cemb = max(cblock)
        
        # Time embedding
        if use_fourier:
            self.emb_t = MPFourierEmbedding(cblock[0])
        else:
            self.emb_t = MPPositionalEmbedding(cblock[0])
        self.emb_t_linear = MPConv(cblock[0], cemb)
        
        # Class label embedding
        self.emb_label = MPConv(label_dim, cemb) if label_dim > 0 else None
        
        self.out_gain = nn.Parameter(torch.zeros([]))
        
        # Encoder
        self.enc = nn.ModuleDict()
        cout = img_channels + 1
        for level, channels in enumerate(cblock):
            res = img_resolution >> level
            if level == 0:
                self.enc[f'{res}x{res}_conv'] = MPConv(cout, channels, kernel=(3, 3))
                cout = channels
            else:
                self.enc[f'{res}x{res}_down'] = Block(cout, cout, cemb, flavor="enc", resample_mode="down")
            
            for i in range(num_blocks):
                cin = cout
                cout = channels
                self.enc[f'{res}x{res}_block{i}'] = Block(cin, cout, cemb, flavor="enc", attention=(res in attn_resolutions))

        # Decoder
        self.dec = nn.ModuleDict()
        for level, channels in reversed(list(enumerate(cblock))):
            res = img_resolution >> level
            if level == len(cblock) - 1:
                self.dec[f'{res}x{res}_in0'] = Block(cout, cout, cemb, flavor="dec", attention=True)
                self.dec[f'{res}x{res}_in1'] = Block(cout, cout, cemb, flavor="dec")
            else:
                self.dec[f'{res}x{res}_up'] = Block(cout, cout, cemb, flavor="dec", resample_mode="up")
            
            for i in range(num_blocks + 1):
                cin = cout + cblock[level] # Skip connection
                cout = channels
                self.dec[f'{res}x{res}_block{i}'] = Block(cin, cout, cemb, flavor="dec", attention=(res in attn_resolutions))
        
        self.out_conv = MPConv(cout, img_channels, kernel=(3, 3))

    def forward(self, x: torch.Tensor, ts: torch.Tensor, class_labels: torch.Tensor) -> torch.Tensor:
        # Embeddings
        emb = mp_silu(self.emb_t_linear(self.emb_t(ts)))
        if self.label_dim > 0:
            class_emb = self.emb_label(class_labels * math.sqrt(self.label_dim))
            emb = mp_sum(emb, class_emb, t=self.label_balance)

        # Encoder
        x = torch.cat([x, torch.ones_like(x[:, :1])], dim=1) # Add constant channel
        skips = {}
        for name, block in self.enc.items():
            x = block(x, emb) if 'block' in name or 'down' in name else block(x)
            skips[name] = x
        
        # Decoder
        keys = list(self.enc.keys())
        for name, block in self.dec.items():
            if 'block' in name:
                x = mp_cat(x, skips[keys.pop()], t=self.concat_balance)
            x = block(x, emb)
            
        return self.out_conv(x, gain=self.out_gain)


class PrecondUNet(nn.Module):
    """The final wrapper model with preconditioning and weight calculation."""
    def __init__(self,
                 img_resolution: int,
                 img_channels: int,
                 label_dim: int = 0,
                 sigma_data: float = 0.5,
                 logvar_channels: int = 128,
                 use_fourier: bool = False,
                 **unet_kwargs):
        super().__init__()
        self.sigma_data = sigma_data
        self.unet = EDM2UNet(
            img_resolution=img_resolution,
            img_channels=img_channels,
            label_dim=label_dim,
            use_fourier=use_fourier,
            **unet_kwargs
        )

        # Layers for the loss weighting
        if use_fourier:
            self.logvar_fourier_t = MPFourierEmbedding(logvar_channels)
        else:
            self.logvar_fourier_t = MPPositionalEmbedding(logvar_channels)
        self.logvar_linear = MPConv(logvar_channels, 1)

    def calc_weight(self, ts: torch.Tensor) -> torch.Tensor:
        """Calculates the time-dependent loss weight."""
        embed = self.logvar_fourier_t(ts)
        logvar = self.logvar_linear(embed)
        return logvar.view(-1)

    def forward(self, xs: torch.Tensor, ts: torch.Tensor, class_labels: Optional[torch.Tensor] = None) -> torch.Tensor:
        # Pre-conditioning
        c_in = 1.0 / self.sigma_data
        c_out = self.sigma_data
        
        # Prepare inputs
        xs_in = (c_in * xs).to(torch.float32)
        ts_in = ts.to(torch.float32)
        labels_in = class_labels.to(torch.float32) if class_labels is not None else torch.zeros(xs.shape[0], 0, device=xs.device)
        
        # Run U-Net and apply post-conditioning
        output = self.unet(xs_in, ts_in, labels_in)
        return c_out * output