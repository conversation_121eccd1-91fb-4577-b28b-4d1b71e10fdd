# utils.py

"""
Training utilities for PyTorch interpolants.

This is a corrected version that enables a faithful translation of the JAX
training state setup, including support for custom schedulers.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import LambdaLR, CosineAnnealingLR
from typing import Dict, Any, Optional, Tuple, Iterable
import math

# --- MODIFIED: Accepts a list of parameters to support masking ---
def setup_optimizer(params_to_optimize: Iterable[torch.nn.Parameter], config) -> optim.Optimizer:
    """
    Setup optimizer based on configuration.
    Accepts a list of parameters to optimize, allowing for freezing certain layers.
    """
    if isinstance(config, dict):
        learning_rate = config['optimization']['learning_rate']
    else:
        learning_rate = config.optimization.learning_rate

    # AdamW is a good default, similar to optax.adam
    return optim.AdamW(
        params_to_optimize,
        lr=learning_rate,
        betas=(0.9, 0.999),
        eps=1e-8
    )

# --- MODIFIED: Added 'sqrt' schedule for faithful JAX translation ---
def setup_scheduler(optimizer: optim.Optimizer, config) -> Optional[optim.lr_scheduler.LRScheduler]:
    """Setup learning rate scheduler based on configuration."""
    if isinstance(config, dict):
        schedule_type = config['optimization'].get('schedule_type', 'cosine')
        total_steps = config['optimization']['total_steps']
        decay_steps = config['optimization'].get('decay_steps', total_steps)
    else:
        schedule_type = getattr(config.optimization, 'schedule_type', 'cosine')
        total_steps = config.optimization.total_steps
        decay_steps = getattr(config.optimization, 'decay_steps', total_steps)
    
    if schedule_type == 'cosine':
        return CosineAnnealingLR(optimizer, T_max=total_steps, eta_min=0.0)
    
    elif schedule_type == 'sqrt':
        # This lambda function replicates the JAX sqrt schedule
        lr_lambda = lambda step: 1.0 / math.sqrt(max(step / decay_steps, 1.0))
        return LambdaLR(optimizer, lr_lambda)
        
    elif schedule_type == 'constant':
        return None
        
    else:
        raise ValueError(f"Unknown schedule type: {schedule_type}")

def clip_gradients(parameters: Iterable[torch.nn.Parameter], max_norm: float) -> float:
    """Clip gradients of an iterable of parameters and return the norm."""
    if max_norm <= 0:
        return 0.0
    
    grad_norm = torch.nn.utils.clip_grad_norm_(parameters, max_norm)
    return grad_norm.item()

def save_checkpoint(
    model: nn.Module,
    optimizer: optim.Optimizer,
    scheduler: Optional[optim.lr_scheduler.LRScheduler],
    ema: Optional[Any],
    step: int,
    loss: float,
    filepath: str,
    **kwargs
):
    """Save training checkpoint."""
    checkpoint = {
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'step': step,
        'loss': loss,
        **kwargs
    }
    
    if scheduler is not None:
        checkpoint['scheduler_state_dict'] = scheduler.state_dict()
    
    if ema is not None:
        checkpoint['ema_state_dict'] = ema.state_dict()
    
    torch.save(checkpoint, filepath)


def load_checkpoint(
    filepath: str,
    model: nn.Module,
    optimizer: Optional[optim.Optimizer] = None,
    scheduler: Optional[optim.lr_scheduler.LRScheduler] = None,
    ema: Optional[Any] = None,
    device: torch.device = None,
) -> Dict[str, Any]:
    """Load training checkpoint."""
    if device is None:
        device = next(model.parameters()).device
    
    checkpoint = torch.load(filepath, map_location=device)
    
    model.load_state_dict(checkpoint['model_state_dict'])
    
    if optimizer is not None and 'optimizer_state_dict' in checkpoint:
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    if scheduler is not None and 'scheduler_state_dict' in checkpoint:
        scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
    
    if ema is not None and 'ema_state_dict' in checkpoint:
        ema.load_state_dict(checkpoint['ema_state_dict'])
    
    return checkpoint

class GradientAccumulator:
    """Utility for gradient accumulation."""
    def __init__(self, accumulation_steps: int = 1):
        self.accumulation_steps = accumulation_steps
        self.current_step = 0
    
    def should_update(self) -> bool:
        self.current_step += 1
        if self.current_step % self.accumulation_steps == 0:
            self.current_step = 0
            return True
        return False

    def scale_loss(self, loss: torch.Tensor) -> torch.Tensor:
        return loss / self.accumulation_steps
    
    def unscale_gradients(self, optimizer):
        """Need to be implemented if using AMP/fp16 scaler."""
        pass

class MovingAverage:
    """Simple moving average tracker."""
    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.values = []
        self.sum = 0.0
    
    def update(self, value: float):
        self.values.append(value)
        self.sum += value
        if len(self.values) > self.window_size:
            self.sum -= self.values.pop(0)
    
    def get_average(self) -> float:
        return self.sum / len(self.values) if self.values else 0.0