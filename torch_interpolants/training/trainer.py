"""
Main trainer class for PyTorch interpolants.

This version is updated to be a faithful translation of the JAX training loop,
including optimizer masking and weight projection.
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from typing import Dict, Any, Optional, Tuple
import time
import logging
from tqdm import tqdm
import wandb
import os

from ..interpolant import Interpolant
from ..losses import setup_loss
# --- MODIFIED: Import the corrected EMAManager and network components ---
from .ema import EMAManager
from .utils import (
    setup_optimizer, setup_scheduler, clip_gradients,
    save_checkpoint, MovingAverage, GradientAccumulator
)
# This assumes edm2_torch.py is in the same directory and contains project_to_sphere
from .edm2_torch import project_to_sphere


class InterpolantTrainer:
    """Main trainer for interpolant models."""
    
    def __init__(
        self,
        model: nn.Module,
        interpolant: Interpolant,
        config: Dict[str, Any],
        device: torch.device = None,
        use_wandb: bool = False,
    ):
        self.model = model
        self.interpolant = interpolant
        self.config = config
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.use_wandb = use_wandb
        
        # Move model to device
        self.model.to(self.device)
        
        # --- ADDED: Filter parameters to freeze Fourier embeddings before creating the optimizer ---
        trainable_params = [
            p for name, p in self.model.named_parameters()
            if "freqs" not in name and "phases" not in name
        ]
        
        # Setup optimizer and scheduler
        self.optimizer = setup_optimizer(trainable_params, config)
        self.scheduler = setup_scheduler(self.optimizer, config)
        
        # Setup loss function
        self.loss_fn = setup_loss(config, model, interpolant)
        
        # --- MODIFIED: Use the corrected EMAManager ---
        if isinstance(config, dict):
            ema_facs = config['training'].get('ema_facs', [0.999, 0.9999])
            accumulation_steps = config['optimization'].get('gradient_accumulation_steps', 1)
            tmax = config['training'].get('tmax', 1.0)
            tmin = config['training'].get('tmin', 0.0)
        else: # Assumes config object has attributes
            ema_facs = getattr(config.training, 'ema_facs', [0.999, 0.9999])
            accumulation_steps = getattr(config.optimization, 'gradient_accumulation_steps', 1)
            tmax = getattr(config.training, 'tmax', 1.0)
            tmin = getattr(config.training, 'tmin', 0.0)

        self.ema = EMAManager(self.model, decay_rates=ema_facs)

        # Training state
        self.step = 0
        self.epoch = 0
        self.best_loss = float('inf')
        self.tmax = tmax
        self.tmin = tmin

        # Metrics tracking
        self.loss_tracker = MovingAverage(window_size=100)
        self.grad_norm_tracker = MovingAverage(window_size=100)

        # Gradient accumulation
        self.grad_accumulator = GradientAccumulator(accumulation_steps)
        
        # Logging
        self.logger = logging.getLogger(__name__)
        
        if self.use_wandb:
            self._init_wandb()
    
    def _init_wandb(self):
        """Initialize Weights & Biases logging."""
        wandb.init(
            project=self.config.get('logging', {}).get('wandb_project', 'torch-interpolants'),
            name=self.config.get('logging', {}).get('wandb_name', 'experiment'),
            entity=self.config.get('logging', {}).get('wandb_entity', None),
            config=self.config,
        )
        wandb.watch(self.model, log='all', log_freq=1000)
    
    def train_step(
        self,
        batch: Tuple[torch.Tensor, torch.Tensor, torch.Tensor],
    ) -> Dict[str, float]:
        """Single training step."""
        x0, x1, labels = batch
        x0 = x0.to(self.device)
        x1 = x1.to(self.device)
        labels = labels.to(self.device)
        
        batch_size = x0.shape[0]
        
        t = torch.rand(batch_size, device=self.device) * (self.tmax - self.tmin) + self.tmin
        
        loss = self.loss_fn(self.model, x0, x1, labels, t)
        loss = self.grad_accumulator.scale_loss(loss)
        loss.backward()
        
        # Use .item() only after ensuring it's not a scaled loss
        metrics = {'loss': loss.item() * self.grad_accumulator.accumulation_steps}
        
        if self.grad_accumulator.should_update():
            # Get clip value from config
            if isinstance(self.config, dict):
                clip_val = self.config['optimization'].get('clip', 5.0)
            else:
                clip_val = getattr(self.config.optimization, 'clip', 5.0)

            # Unscale gradients before clipping
            self.grad_accumulator.unscale_gradients(self.optimizer)
            grad_norm = clip_gradients(self.model.parameters(), clip_val)
            metrics['grad_norm'] = grad_norm
            
            self.optimizer.step()
            self.optimizer.zero_grad()
            
            # --- ADDED: Critical weight projection step after optimizer update ---
            project_to_sphere(self.model)
            
            self.ema.update()
            
            if self.scheduler is not None:
                self.scheduler.step()
                metrics['lr'] = self.scheduler.get_last_lr()[0]
            
            self.step += 1
        
        return metrics
    
    def train_epoch(self, dataloader: DataLoader) -> Dict[str, float]:
        """Train for one epoch."""
        self.model.train()
        
        pbar = tqdm(dataloader, desc=f'Epoch {self.epoch}')
        for batch in pbar:
            metrics = self.train_step(batch)
            
            if 'loss' in metrics:
                self.loss_tracker.update(metrics['loss'])
            if 'grad_norm' in metrics:
                self.grad_norm_tracker.update(metrics['grad_norm'])
            
            pbar.set_postfix({
                'loss': f"{self.loss_tracker.get_average():.4f}",
                'step': self.step
            })
            
            if isinstance(self.config, dict):
                log_freq = self.config['logging'].get('log_freq', 100)
                save_freq = self.config['logging'].get('save_freq', 10000)
                total_steps = self.config['optimization']['total_steps']
            else:
                log_freq = getattr(self.config.logging, 'log_freq', 100)
                save_freq = getattr(self.config.logging, 'save_freq', 10000)
                total_steps = self.config.optimization.total_steps

            if self.step > 0 and self.step % log_freq == 0:
                self._log_metrics(metrics)

            if self.step > 0 and self.step % save_freq == 0:
                self._save_checkpoint()

            if self.step >= total_steps:
                break
        
        return {
            'avg_loss': self.loss_tracker.get_average(),
            'avg_grad_norm': self.grad_norm_tracker.get_average()
        }

    def train(self, dataloader: DataLoader, num_epochs: Optional[int] = None):
        """Main training loop."""
        if isinstance(self.config, dict):
            total_steps = self.config['optimization']['total_steps']
        else:
            total_steps = self.config.optimization.total_steps

        if num_epochs is None:
            steps_per_epoch = len(dataloader) // self.grad_accumulator.accumulation_steps
            num_epochs = (total_steps + steps_per_epoch - 1) // steps_per_epoch

        self.logger.info(f"Starting training for {num_epochs} epochs ({total_steps} total steps).")
        
        for epoch in range(num_epochs):
            self.epoch = epoch
            start_time = time.time()
            epoch_metrics = self.train_epoch(dataloader)
            epoch_time = time.time() - start_time
            
            log_msg = (
                f"Epoch {epoch} completed in {epoch_time:.2f}s. "
                f"Avg loss: {epoch_metrics.get('avg_loss', 0):.4f}"
            )
            self.logger.info(log_msg)
            
            if self.use_wandb:
                wandb.log({
                    'epoch': epoch,
                    'epoch_time': epoch_time,
                    **epoch_metrics
                }, step=self.step)
            
            if self.step >= total_steps:
                self.logger.info("Target total steps reached. Finishing training.")
                break
        
        self._save_checkpoint(final=True)
        if self.use_wandb:
            wandb.finish()
    
    def _log_metrics(self, metrics: Dict[str, float]):
        """Log training metrics."""
        log_data = {k: v for k, v in metrics.items() if v is not None}
        if self.use_wandb:
            wandb.log(log_data, step=self.step)
        
        # Console logging
        log_str = f"Step {self.step}: "
        log_str += ", ".join([f"{k}: {v:.4f}" for k, v in log_data.items()])
        self.logger.info(log_str)
    
    def _save_checkpoint(self, final: bool = False):
        """Save training checkpoint."""
        if isinstance(self.config, dict):
            output_folder = self.config['logging'].get('output_folder', './checkpoints')
        else:
            output_folder = getattr(self.config.logging, 'output_folder', './checkpoints')
        
        os.makedirs(output_folder, exist_ok=True)
        
        filename = 'final_model.pt' if final else f'checkpoint_step_{self.step}.pt'
        filepath = os.path.join(output_folder, filename)
        
        # Pass the EMAManager directly to save its state_dict
        save_checkpoint(
            model=self.model,
            optimizer=self.optimizer,
            scheduler=self.scheduler,
            ema=self.ema, # <-- MODIFIED
            step=self.step,
            loss=self.loss_tracker.get_average(),
            filepath=filepath,
            epoch=self.epoch,
            config=self.config,
        )
        self.logger.info(f"Checkpoint saved: {filepath}")
    
    def sample(
        self,
        num_samples: int,
        num_steps: int = 50,
        method: str = "heun",
        class_labels: Optional[torch.Tensor] = None,
    ) -> torch.Tensor:
        """Generate samples using the trained model."""
        # --- MODIFIED: Use corrected sampler and EMA model ---
        from ..samplers import batch_sample 

        # Retrieve the best EMA model for sampling
        model_for_sampling = self.ema.get_ema_model(self.ema.decay_rates[-1])
        model_for_sampling.eval()
        
        if isinstance(self.config, dict):
            image_dims = self.config['problem']['image_dims']
        else:
            image_dims = self.config.problem.image_dims
        x0_base = torch.randn(num_samples, *image_dims, device=self.device)
        
        if class_labels is None:
            # Create dummy labels if model is conditional but no labels are provided
            label_dim = getattr(self.config.network, 'label_dim', 0)
            if label_dim > 0:
                class_labels = torch.zeros(num_samples, label_dim, device=self.device)
        
        with torch.no_grad():
            samples = batch_sample(
                model=model_for_sampling,
                x0s=x0_base,
                N=num_steps,
                labels=class_labels,
            )
        
        return samples