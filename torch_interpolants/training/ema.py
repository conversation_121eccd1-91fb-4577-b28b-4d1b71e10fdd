"""
PyTorch implementation for Exponential Moving Average (EMA).

This module provides a faithful translation of the EMA logic from the original
JAX implementation, specifically from `state_utils.py` and `updates.py`.
"""

import copy
from typing import List

import torch
import torch.nn as nn


class EMAManager(nn.Module):
    """
    Manages the Exponential Moving Average of a model's parameters.

    This class holds the primary training model and one or more "shadow"
    models whose parameters are an exponential moving average of the
    primary model's parameters. This is the PyTorch-idiomatic equivalent
    of the `EMATrainState` class in the JAX implementation.
    """

    def __init__(self, model: nn.Module, decay_rates: List[float]):
        """
        Initializes the EMAManager.

        Args:
            model (nn.Module): The model to be trained.
            decay_rates (List[float]): A list of EMA decay factors (e.g., [0.999, 0.9999]).
        """
        super().__init__()
        if not (isinstance(decay_rates, list) and all(0.0 < d < 1.0 for d in decay_rates)):
            raise ValueError("'decay_rates' must be a list of floats between 0 and 1.")

        # Store the original model; this is the one that receives gradients.
        self.model = model

        self.decay_rates = decay_rates
        self.ema_models = nn.ModuleDict()

        # Create a deep copy of the model for each decay rate. These are the "shadow" models.
        # Using a ModuleDict makes saving and loading the state straightforward.
        for decay in decay_rates:
            try:
                ema_model = copy.deepcopy(model)
                # The key must be a valid ModuleDict key (string).
                key = f"ema_model_{str(decay).replace('.', '_')}"
                self.ema_models[key] = ema_model
            except Exception as e:
                print(f"Could not deepcopy the model. Ensure it is copyable. Error: {e}")
                raise

    def update(self):
        """
        Updates the EMA parameters for all shadow models.

        This method should be called after each training step (e.g., after optimizer.step()).
        It performs the update: `ema_param = decay * ema_param + (1 - decay) * current_param`.
        """
        with torch.no_grad():
            # Iterate through the registered decay rates and their corresponding shadow models.
            for decay, (key, ema_model) in zip(self.decay_rates, self.ema_models.items()):
                # Zip the parameters of the shadow model and the primary training model.
                for ema_param, param in zip(ema_model.parameters(), self.model.parameters()):
                    if not param.requires_grad:
                        continue # Skip non-trainable parameters

                    # Apply the EMA update rule.
                    ema_param.data.mul_(decay).add_(param.data, alpha=1 - decay)

    def get_ema_model(self, decay_rate: float) -> nn.Module:
        """
        Retrieves a specific shadow model for evaluation or inference.

        Args:
            decay_rate (float): The decay rate of the desired EMA model.

        Returns:
            nn.Module: The shadow model with the specified decay rate.
        """
        if decay_rate not in self.decay_rates:
            raise ValueError(
                f"Decay rate {decay_rate} not found. Available rates: {self.decay_rates}"
            )
        key = f"ema_model_{str(decay_rate).replace('.', '_')}"
        return self.ema_models[key]

    def forward(self, *args, **kwargs):
        """
        The forward pass uses the primary training model.

        This allows the EMAManager to be used as a drop-in replacement for the
        original model in the training loop.
        """
        return self.model(*args, **kwargs)