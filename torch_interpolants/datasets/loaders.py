"""
Dataset loaders for MNIST, CIFAR-10, and Checkerboard.

Translation and adaptation of JAX dataset loading to PyTorch.
"""

import torch
from torch.utils.data import Dataset, DataLoader
import torchvision
import torchvision.transforms as transforms
from typing import Tuple, Optional, Callable
import numpy as np


class InterpolantDataset(Dataset):
    """Dataset wrapper for interpolant training."""
    
    def __init__(
        self,
        dataset: Dataset,
        base_distribution: str = "gaussian",
        gaussian_scale: str = "adaptive",
        conditional: bool = False,
    ):
        self.dataset = dataset
        self.base_distribution = base_distribution
        self.gaussian_scale = gaussian_scale
        self.conditional = conditional
        
        # Compute data statistics for adaptive scaling
        if gaussian_scale == "adaptive":
            self._compute_data_stats()
    
    def _compute_data_stats(self):
        """Compute data statistics for adaptive Gaussian scaling."""
        # Sample a subset of data to compute statistics
        sample_size = min(1000, len(self.dataset))
        indices = torch.randperm(len(self.dataset))[:sample_size]
        
        data_samples = []
        for i in range(len(indices)):
            # Use the index from the random permutation
            idx = indices[i].item()
            data, _ = self.dataset[idx]
            data_samples.append(data.flatten())
        
        data_tensor = torch.stack(data_samples)
        self.data_std = torch.std(data_tensor).item()
        self.data_mean = torch.mean(data_tensor).item()
    
    def __len__(self) -> int:
        return len(self.dataset)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Get a training sample (x0, x1, label)."""
        x1, label = self.dataset[idx]
        
        # Ensure x1 is a tensor and normalize to [-1, 1] if needed
        if not isinstance(x1, torch.Tensor):
            x1 = torch.tensor(x1, dtype=torch.float32)
        
        # Sample from base distribution (x0)
        if self.base_distribution == "gaussian":
            if self.gaussian_scale == "adaptive" and hasattr(self, 'data_std'):
                x0 = torch.randn_like(x1) * self.data_std
            else:
                x0 = torch.randn_like(x1)
        else:
            raise ValueError(f"Unknown base distribution: {self.base_distribution}")
        
        # Convert label to tensor
        if not isinstance(label, torch.Tensor):
            label = torch.tensor(label, dtype=torch.long)
        
        # For unconditional training, use dummy labels
        if not self.conditional:
            label = torch.zeros_like(label)
        
        return x0, x1, label


def get_mnist_dataloader(
    batch_size: int = 128,
    train: bool = True,
    download: bool = True,
    data_root: str = "./data",
    conditional: bool = False,
    num_workers: int = 4,
    **kwargs
) -> DataLoader:
    """Get MNIST dataloader for interpolant training."""
    
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.5,), (0.5,))  # Normalize to [-1, 1]
    ])
    
    mnist_dataset = torchvision.datasets.MNIST(
        root=data_root,
        train=train,
        download=download,
        transform=transform
    )
    
    interpolant_dataset = InterpolantDataset(
        dataset=mnist_dataset,
        base_distribution="gaussian",
        gaussian_scale="adaptive",
        conditional=conditional,
    )
    
    dataloader = DataLoader(
        interpolant_dataset,
        batch_size=batch_size,
        shuffle=train,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available(),
        drop_last=train,
        **kwargs
    )
    
    return dataloader


def get_cifar10_dataloader(
    batch_size: int = 128,
    train: bool = True,
    download: bool = True,
    data_root: str = "./data",
    conditional: bool = False,
    num_workers: int = 4,
    augment: bool = True,
    **kwargs
) -> DataLoader:
    """Get CIFAR-10 dataloader for interpolant training."""
    
    if train and augment:
        transform = transforms.Compose([
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.ToTensor(),
            transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
        ])
    else:
        transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
        ])
    
    cifar10_dataset = torchvision.datasets.CIFAR10(
        root=data_root,
        train=train,
        download=download,
        transform=transform
    )
    
    interpolant_dataset = InterpolantDataset(
        dataset=cifar10_dataset,
        base_distribution="gaussian",
        gaussian_scale="adaptive",
        conditional=conditional,
    )
    
    dataloader = DataLoader(
        interpolant_dataset,
        batch_size=batch_size,
        shuffle=train,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available(),
        drop_last=train,
        **kwargs
    )
    
    return dataloader


class CheckerDataset(Dataset):
    """
    2D checker pattern dataset for testing.
    This version uses efficient rejection sampling.
    """
    
    def __init__(self, num_samples: int = 10000, n_squares: int = 4):
        self.num_samples = num_samples
        self.n_squares = n_squares
        self.data = self._generate_checker_data()
    
    def _generate_checker_data(self) -> torch.Tensor:
        """
        Generate 2D checker pattern data on [-1,1] x [-1,1] via rejection sampling.
        """
        total_samples = 0
        samples = torch.empty(0, 2)

        while total_samples < self.num_samples:
            # Generate uniform samples on the [0,1] x [0,1] square
            curr_samples = torch.rand(self.num_samples * 2, 2)

            # Determine which square each point falls into
            x_idx = torch.floor(curr_samples[:, 0] * self.n_squares)
            y_idx = torch.floor(curr_samples[:, 1] * self.n_squares)

            # Keep points that fall in "white squares" of the checkerboard
            mask = (x_idx + y_idx) % 2 == 0
            curr_samples = curr_samples[mask]

            # Add the valid samples to our collection
            samples = torch.cat((samples, curr_samples), dim=0)
            total_samples = samples.shape[0]

        # Trim to the requested number of samples and scale to [-1, 1]
        return 2 * samples[:self.num_samples] - 1
    
    def __len__(self) -> int:
        return self.num_samples
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        return self.data[idx], torch.tensor(0)  # Dummy label


def get_checker_dataloader(
    batch_size: int = 128,
    num_samples: int = 10000,
    n_squares: int = 4,
    num_workers: int = 4,
    **kwargs
) -> DataLoader:
    """Get checker pattern dataloader for testing."""
    
    checker_dataset = CheckerDataset(num_samples=num_samples, n_squares=n_squares)
    
    interpolant_dataset = InterpolantDataset(
        dataset=checker_dataset,
        base_distribution="gaussian",
        gaussian_scale="adaptive",
        conditional=False,
    )
    
    dataloader = DataLoader(
        interpolant_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available(),
        drop_last=True,
        **kwargs
    )
    
    return dataloader


def create_dataloader_from_tensors(
    data: torch.Tensor,
    labels: Optional[torch.Tensor] = None,
    batch_size: int = 128,
    shuffle: bool = True,
    **kwargs
) -> DataLoader:
    """Create dataloader from tensor data."""
    
    if labels is None:
        labels = torch.zeros(len(data), dtype=torch.long)
    
    dataset = torch.utils.data.TensorDataset(data, labels)
    
    interpolant_dataset = InterpolantDataset(
        dataset=dataset,
        base_distribution="gaussian",
        gaussian_scale="adaptive",
        conditional=(labels.unique().numel() > 1),
    )
    
    return DataLoader(
        interpolant_dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        **kwargs
    )
