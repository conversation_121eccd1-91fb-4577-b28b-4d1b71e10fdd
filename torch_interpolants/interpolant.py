"""
PyTorch implementation of stochastic interpolants.
"""

import torch
import torch.nn as nn
from typing import Callable, Union
import math

class Interpolant(nn.Module):
    """
    Basic class for a stochastic interpolant.
    The core methods are now batch-aware and handle broadcasting correctly.
    Note: The convention here is that x0 is noise and x1 is data.
    """
    def __init__(
        self,
        alpha: Callable[[Union[torch.Tensor, float]], Union[torch.Tensor, float]],
        beta: Callable[[Union[torch.Tensor, float]], Union[torch.Tensor, float]],
        alpha_dot: Callable[[Union[torch.Tensor, float]], Union[torch.Tensor, float]],
        beta_dot: Callable[[Union[torch.Tensor, float]], Union[torch.Tensor, float]],
    ):
        super().__init__()
        self.alpha = alpha
        self.beta = beta
        self.alpha_dot = alpha_dot
        self.beta_dot = beta_dot

    def _reshape_t_for_broadcast(self, t: torch.Tensor, x: torch.Tensor) -> torch.Tensor:
        """Reshapes t to be broadcastable with x."""
        if not isinstance(t, torch.Tensor):
            return t
        # Reshape t to (B, 1, 1, ...) to match x's dimensions
        return t.view(-1, *([1] * (x.dim() - 1)))

    def calc_It(self, t: Union[torch.Tensor, float], x0: torch.Tensor, x1: torch.Tensor) -> torch.Tensor:
        """Compute the interpolant I_t(x0, x1) = alpha(t)*x0 + beta(t)*x1."""
        alpha_t = self._reshape_t_for_broadcast(self.alpha(t), x0)
        beta_t = self._reshape_t_for_broadcast(self.beta(t), x1)
        return alpha_t * x0 + beta_t * x1

    def calc_It_dot(self, t: Union[torch.Tensor, float], x0: torch.Tensor, x1: torch.Tensor) -> torch.Tensor:
        """Compute the time derivative of the interpolant."""
        alpha_dot_t = self._reshape_t_for_broadcast(self.alpha_dot(t), x0)
        beta_dot_t = self._reshape_t_for_broadcast(self.beta_dot(t), x1)
        return alpha_dot_t * x0 + beta_dot_t * x1

    def calc_target(self, t: Union[torch.Tensor, float], x0: torch.Tensor, x1: torch.Tensor, target_type: str) -> torch.Tensor:
        """Compute the target for learning."""
        if target_type == "velocity":
            return self.calc_It_dot(t, x0, x1)
        
        alpha_t = self._reshape_t_for_broadcast(self.alpha(t), x0)
        if target_type == "score":
            return -x0 / alpha_t
        elif target_type == "noise":
            # The model is conditioned on I_t and predicts the noise x0
            return x0
        elif target_type == "denoiser":
            # The model is conditioned on I_t and predicts the data x1
            return x1
        else:
            raise ValueError(f"Target type {target_type} not recognized.")


def setup_interpolant(config) -> Interpolant:
    """Setup interpolant based on configuration."""
    if isinstance(config, dict):
        interp_type = config['problem']['interp_type']
        tmax = config['problem'].get('tmax', 1.0)
        tf = config['problem'].get('tf', 1.0)
    else: # Assumes config object has attributes
        interp_type = config.problem.interp_type
        tmax = getattr(config.problem, 'tmax', 1.0)
        tf = getattr(config.problem, 'tf', 1.0)

    pi = torch.tensor(math.pi)

    if interp_type == "linear":
        return Interpolant(
            alpha=lambda t: 1.0 - t,
            beta=lambda t: t,
            alpha_dot=lambda t: -1.0,
            beta_dot=lambda t: 1.0,
        )
    elif interp_type == "trig":
        return Interpolant(
            alpha=lambda t: torch.cos(pi / 2 * t),
            beta=lambda t: torch.sin(pi / 2 * t),
            alpha_dot=lambda t: -pi / 2 * torch.sin(pi / 2 * t),
            beta_dot=lambda t: pi / 2 * torch.cos(pi / 2 * t),
        )
    elif interp_type == "vp_diffusion":
        return Interpolant(
            alpha=lambda t: torch.sqrt(1 - torch.exp(2 * (t - tmax))),
            beta=lambda t: torch.exp(t - tmax),
            alpha_dot=lambda t: -torch.exp(2 * (t - tmax)) / torch.sqrt(1 - torch.exp(2 * (t - tmax))),
            beta_dot=lambda t: torch.exp(t - tmax),
        )
    elif interp_type == "vp_diffusion_logscale":
        return Interpolant(
            alpha=lambda t: torch.sqrt(1 - t**2),
            beta=lambda t: t,
            alpha_dot=lambda t: -t / torch.sqrt(1 - t**2),
            beta_dot=lambda t: 1.0,
        )
    elif interp_type == "ve_diffusion":
        return Interpolant(
            alpha=lambda t: tf - t,
            beta=lambda t: 1.0,
            alpha_dot=lambda t: -1.0,
            beta_dot=lambda t: 0.0,
        )
    else:
        raise ValueError(f"Interpolant type {interp_type} not recognized.")