"""
PyTorch implementation of the EDM2 UNet architecture.

This is a corrected and robust translation of the original JAX/Flax 
implementation from 'jax-interpolants/py/common/edm2_net.py'.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional, List, Tuple

# -- Helper Functions --

def mp_silu(x: torch.Tensor) -> torch.Tensor:
    """Magnitude-preserving SiLU activation."""
    return F.silu(x) / 0.596

def mp_sum(a: torch.Tensor, b: torch.Tensor, t: float = 0.5) -> torch.Tensor:
    """Magnitude-preserving sum."""
    return (a * (1 - t) + b * t) / math.sqrt((1 - t) ** 2 + t**2)

def mp_cat(a: torch.Tensor, b: torch.Tensor, dim: int = 1, t: float = 0.5) -> torch.Tensor:
    """Magnitude-preserving concatenation."""
    Na = a.shape[dim]
    Nb = b.shape[dim]
    C = math.sqrt((Na + Nb) / ((1 - t) ** 2 + t**2))
    wa = C / math.sqrt(Na) * (1 - t)
    wb = C / math.sqrt(Nb) * t
    return torch.cat([wa * a, wb * b], dim=dim)

def normalize(x: torch.Tensor, dim: Optional[Tuple[int, ...]] = None, eps: float = 1e-4) -> torch.Tensor:
    """Normalize tensor to unit magnitude with respect to given dimensions."""
    if dim is None:
        dim = tuple(range(1, x.dim()))
    
    norm = torch.sqrt(torch.sum(x.pow(2), dim=dim, keepdim=True))
    norm_size = math.prod([x.shape[d] for d in dim])
    x_size = x.numel()
    
    norm = eps + norm * math.sqrt(norm_size / x_size)
    return x / norm

def resample(x: torch.Tensor, f: List[int] = [1, 3, 3, 1], mode: str = "keep") -> torch.Tensor:
    """Upsample or downsample a tensor with a given filter."""
    if mode == "keep":
        return x

    f_tensor = torch.tensor(f, dtype=x.dtype, device=x.device)
    f_tensor = f_tensor / f_tensor.sum()
    f_outer = torch.outer(f_tensor, f_tensor)[None, None, :, :]
    
    C = x.shape[1]
    f_expanded = f_outer.expand(C, -1, -1, -1)
    pad = (len(f) - 1) // 2

    if mode == "down":
        return F.conv2d(x, f_expanded, stride=2, padding=pad, groups=C)
    if mode == "up":
        return F.conv_transpose2d(x, f_expanded * 4, stride=2, padding=pad, groups=C)
    raise ValueError(f"Unknown resample mode: {mode}")

# -- Embedding Layers --

class MPFourierEmbedding(nn.Module):
    def __init__(self, dim: int, bandwidth: float = 1.0):
        super().__init__()
        self.register_buffer('freqs', torch.randn(dim) * bandwidth * 2 * math.pi)
        self.register_buffer('phases', torch.rand(dim) * 2 * math.pi)

    def forward(self, t: torch.Tensor) -> torch.Tensor:
        angles = t.float().view(-1, 1) * self.freqs + self.phases
        return torch.cos(angles) * math.sqrt(2.0)

class MPPositionalEmbedding(nn.Module):
    def __init__(self, dim: int, max_period: float = 10000.0):
        super().__init__()
        if dim % 2 != 0: raise ValueError("Embedding dimension must be even.")
        half = dim // 2
        freqs = torch.exp(-math.log(max_period) * torch.arange(half, dtype=torch.float32) / half)
        self.register_buffer('freqs', freqs)

    def forward(self, t: torch.Tensor) -> torch.Tensor:
        args = t.float().view(-1, 1) * self.freqs
        cos_emb = torch.cos(args) * math.sqrt(2.0)
        sin_emb = torch.sin(args) * math.sqrt(2.0)
        return torch.cat([cos_emb, sin_emb], dim=-1)

# -- Core Building Blocks --

class MPConv(nn.Module):
    def __init__(self, in_channels: int, out_channels: int, kernel: Optional[Tuple[int, int]] = None):
        super().__init__()
        self.is_linear = (kernel is None)
        if self.is_linear:
            self.mpconv_weight = nn.Parameter(torch.randn(out_channels, in_channels))
        else:
            self.mpconv_weight = nn.Parameter(torch.randn(out_channels, in_channels, *kernel))
    
    def forward(self, x: torch.Tensor, gain: float = 1.0) -> torch.Tensor:
        w = self.mpconv_weight.float()
        w_norm = normalize(w) # <-- CORRECTED: Use the global normalize function
        w_size = w[0].numel()
        w_scaled = w_norm * (gain / math.sqrt(w_size))
        w_scaled = w_scaled.to(x.dtype)

        if self.is_linear:
            return F.linear(x, w_scaled)
        else:
            padding = self.mpconv_weight.shape[-1] // 2
            return F.conv2d(x, w_scaled, padding=padding)

class Block(nn.Module):
    def __init__(self, in_channels: int, out_channels: int, emb_channels: int, flavor: str, resample_mode: str = "keep", attention: bool = False, **block_kwargs):
        super().__init__()
        self.flavor = flavor
        self.resample_mode = resample_mode
        channels_per_head = block_kwargs.get('channels_per_head', 64)
        self.num_heads = out_channels // channels_per_head if attention else 0
        self.res_balance = block_kwargs.get('res_balance', 0.3)
        self.attn_balance = block_kwargs.get('attn_balance', 0.3)

        self.emb_gain = nn.Parameter(torch.zeros([]))
        self.conv_res0 = MPConv(out_channels if flavor == "enc" else in_channels, out_channels, kernel=(3, 3))
        self.emb_linear = MPConv(emb_channels, out_channels)
        self.conv_res1 = MPConv(out_channels, out_channels, kernel=(3, 3))
        self.dropout_layer = nn.Dropout(block_kwargs.get('dropout', 0.0))
        self.conv_skip = MPConv(in_channels, out_channels, kernel=(1, 1)) if in_channels != out_channels else nn.Identity()

        if self.num_heads > 0:
            self.attn_qkv = MPConv(out_channels, out_channels * 3, kernel=(1, 1))
            self.attn_proj = MPConv(out_channels, out_channels, kernel=(1, 1))
    
    def forward(self, x: torch.Tensor, emb: torch.Tensor) -> torch.Tensor:
        x_res = x
        x_res = resample(x_res, mode=self.resample_mode)
        if self.flavor == "enc":
            x_res = self.conv_skip(x_res)
        
        y = mp_silu(x_res)
        y = self.conv_res0(y)
        c = self.emb_linear(emb, gain=self.emb_gain) + 1
        y = mp_silu(y * c.view(c.shape[0], -1, 1, 1))
        y = self.dropout_layer(y)
        y = self.conv_res1(y)
        
        if self.flavor == "dec":
            x_res = self.conv_skip(x_res)
        x = mp_sum(x_res, y, t=self.res_balance)
        
        if self.num_heads > 0:
            y_attn = self.attn_qkv(x)
            q, k, v = y_attn.view(x.shape[0], self.num_heads, -1, 3, x.shape[2] * x.shape[3]).unbind(3)
            q = normalize(q, dim=(2,))
            k = normalize(k, dim=(2,))
            y_attn = F.scaled_dot_product_attention(q, k, v)
            y_attn = self.attn_proj(y_attn.view(*x.shape))
            x = mp_sum(x, y_attn, t=self.attn_balance)
            
        return x

class EDM2UNet(nn.Module):
    def __init__(self, img_resolution: int, img_channels: int, label_dim: int, model_channels: int = 192, channel_mult: List[int] = [1, 2, 3, 4], **kwargs):
        super().__init__()
        self.label_dim = label_dim
        self.label_balance = kwargs.get('label_balance', 0.5)
        self.concat_balance = kwargs.get('concat_balance', 0.5)
        
        cblock = [model_channels * m for m in channel_mult]
        cemb = max(cblock)
        
        # Embeddings
        if kwargs.get('use_fourier', False):
            self.emb_t = MPFourierEmbedding(cblock[0], bandwidth=kwargs.get('fourier_bandwidth', 1.0))
        else:
            self.emb_t = MPPositionalEmbedding(cblock[0])
        self.emb_t_linear = MPConv(cblock[0], cemb)
        self.emb_label = MPConv(label_dim, cemb) if label_dim > 0 else None
        
        self.out_gain = nn.Parameter(torch.zeros([]))
        
        # --- CORRECTED: Robust U-Net construction ---
        # 1. Build Encoder
        self.enc = nn.ModuleDict()
        skips = []
        cout = img_channels + 1
        for level, channels in enumerate(cblock):
            res = img_resolution >> level
            attn = (res in kwargs.get('attn_resolutions', []))
            if level == 0:
                self.enc[f'{res}x{res}_conv'] = MPConv(cout, channels, kernel=(3, 3))
                cout = channels
            else:
                self.enc[f'{res}x{res}_down'] = Block(cout, cout, cemb, "enc", resample_mode="down", **kwargs)
            skips.append(cout)
            
            for i in range(kwargs.get('num_blocks', 3)):
                self.enc[f'{res}x{res}_block{i}'] = Block(cout, channels, cemb, "enc", attention=attn, **kwargs)
                cout = channels
                skips.append(cout)
        
        # 2. Build Decoder
        self.dec = nn.ModuleDict()
        for level, channels in reversed(list(enumerate(cblock))):
            res = img_resolution >> level
            attn = (res in kwargs.get('attn_resolutions', []))
            if level == len(cblock) - 1:
                self.dec[f'{res}x{res}_in0'] = Block(cout, cout, cemb, "dec", attention=True, **kwargs)
                self.dec[f'{res}x{res}_in1'] = Block(cout, cout, cemb, "dec", **kwargs)
            else:
                self.dec[f'{res}x{res}_up'] = Block(cout, cout, cemb, "dec", resample_mode="up", **kwargs)
            
            for i in range(kwargs.get('num_blocks', 3) + 1):
                cin = cout + skips.pop()
                self.dec[f'{res}x{res}_block{i}'] = Block(cin, channels, cemb, "dec", attention=attn, **kwargs)
                cout = channels
        
        self.out_conv = MPConv(cout, img_channels, kernel=(3, 3))

    def forward(self, x: torch.Tensor, ts: torch.Tensor, class_labels: Optional[torch.Tensor]) -> torch.Tensor:
        # Process embeddings
        emb = mp_silu(self.emb_t_linear(self.emb_t(ts)))
        if self.emb_label is not None and class_labels is not None:
            # The label dim is known from init, no need for sqrt of shape
            class_emb = self.emb_label(class_labels * math.sqrt(self.label_dim))
            emb = mp_sum(emb, class_emb, t=self.label_balance)

        # Add the constant channel to the input image
        x = torch.cat([x, torch.ones_like(x[:, :1])], dim=1)

        skips_data = []

        # --- CORRECTED ENCODER LOOP ---
        # Iterate over names and modules, like in JAX
        for name, block in self.enc.items():
            # The initial convolution does not take an embedding
            if "conv" in name:
                x = block(x)
            else:
                x = block(x, emb)
            skips_data.append(x)

        # --- CORRECTED DECODER LOOP ---
        for name, block in self.dec.items():
            # For blocks (not upsampling layers), add the skip connection
            if "block" in name:
                x = mp_cat(x, skips_data.pop(), t=self.concat_balance)
            x = block(x, emb)

        # Final output convolution
        return self.out_conv(x, gain=self.out_gain)

class PrecondUNet(nn.Module):
    def __init__(self, img_resolution: int, img_channels: int, label_dim: int = 0, sigma_data: float = 0.5, **unet_kwargs):
        super().__init__()
        self.sigma_data = sigma_data
        self.unet = EDM2UNet(img_resolution, img_channels, label_dim, **unet_kwargs)

        logvar_channels = unet_kwargs.get('logvar_channels', 128)
        if unet_kwargs.get("use_fourier", False):
            self.logvar_fourier_t = MPFourierEmbedding(logvar_channels, bandwidth=unet_kwargs.get("fourier_bandwidth", 1.0))
        else:
            self.logvar_fourier_t = MPPositionalEmbedding(logvar_channels)
        self.logvar_linear = MPConv(logvar_channels, 1)

    def calc_weight(self, ts: torch.Tensor) -> torch.Tensor:
        embed = self.logvar_fourier_t(ts)
        logvar = self.logvar_linear(embed)
        return logvar.view(-1)

    def forward(self, ts: torch.Tensor, xs: torch.Tensor, class_labels: Optional[torch.Tensor] = None) -> torch.Tensor:
        c_in = 1.0 / self.sigma_data
        c_out = self.sigma_data
        
        xs_in = (c_in * xs).to(torch.float32)
        ts_in = ts.to(torch.float32)
        
        # Ensure labels are float and handled correctly if None
        labels_in = class_labels.float() if class_labels is not None and self.unet.label_dim > 0 else None
        
        output = self.unet(xs_in, ts_in, labels_in)
        return c_out * output