"""
PyTorch implementation of samplers for stochastic interpolants.
"""

import torch
import torch.nn as nn
from typing import Callable

def sample(
    model: nn.Module,
    x0: torch.Tensor,
    N: int,
    label: int,
) -> torch.Tensor:
    """
    Second-order Heun (explicit trapezoidal) integrator for dx/dt = v(t, x).
    This function processes a single data point (no batch dimension).
    """
    # Create the time steps
    ts = torch.linspace(0.0, 1.0, N + 1, device=x0.device, dtype=x0.dtype)
    dts = ts[1:] - ts[:-1]  # Shape: (N,)

    # Ensure model is in evaluation mode
    model.eval()
    x = x0.clone()

    for idx in range(N):
        t = ts[idx]
        dt = dts[idx]

        # Convert scalar time to a tensor for the model
        t_tensor = torch.tensor(t, device=x0.device)
        
        # Predictor step
        v0 = model(t_tensor, x, label=label)
        x_pred = x + dt * v0

        # Corrector step
        t_next_tensor = torch.tensor(t + dt, device=x0.device)
        v1 = model(t_next_tensor, x_pred, label=label)
        
        # Update step
        x = x + 0.5 * dt * (v0 + v1)

    return x


def sample_euler(
    model: nn.Module,
    x0: torch.Tensor,
    N: int,
    label: int,
) -> torch.Tensor:
    """
    Euler integrator for dx/dt = v(t, x).
    This function processes a single data point (no batch dimension).
    """
    ts = torch.linspace(0.0, 1.0, N + 1, device=x0.device, dtype=x0.dtype)
    dts = ts[1:] - ts[:-1]

    model.eval()
    x = x0.clone()

    for idx in range(N):
        t = ts[idx]
        dt = dts[idx]

        t_tensor = torch.tensor(t, device=x0.device)
        v = model(t_tensor, x, label=label)
        x = x + dt * v

    return x


def batch_sample(
    model: nn.Module,
    x0s: torch.Tensor,
    N: int,
    labels: torch.Tensor,
) -> torch.Tensor:
    """
    Batch unconditional sampling using the Heun method (`sample`).

    This function is a direct equivalent of the jax version, which uses
    `jax.vmap` to vectorize the `sample` function.
    """
    return torch.vmap(sample, in_dims=(None, 0, None, 0))(model, x0s, N, labels)