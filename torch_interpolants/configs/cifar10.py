"""
CIFAR-10 configuration for PyTorch interpolants.

Translation of jax-interpolants CIFAR-10 config.
"""

from typing import Dict, Any


def get_cifar10_config(
    dataset_location: str = "./data",
    output_folder: str = "./outputs",
    wandb_entity: str = None,
) -> Dict[str, Any]:
    """Get CIFAR-10 configuration."""
    
    config = {
        # Training config
        'training': {
            'shuffle': True,
            'conditional': False,
            'loss_type': "velocity",
            'class_dropout': 0.0,
            'tmin': 0.0,
            'tmax': 1.0,
            'seed': 42,
            'ema_facs': [0.999, 0.9999],
        },
        
        # Problem config
        'problem': {
            'n': 60000,
            'd': 3072,  # 32*32*3
            'target': "cifar10",
            'image_dims': (3, 32, 32),
            'num_classes': 10,
            'dataset_location': dataset_location,
            'interp_type': "linear",
            'base': "gaussian",
            'gaussian_scale': "adaptive",
        },
        
        # Optimization config
        'optimization': {
            'bs': 128,
            'learning_rate': 1e-3,
            'clip': 10.0,
            'total_steps': 200_000_000,
            'schedule_type': "sqrt",
            'decay_steps': 35000,
        },
        
        # Logging config
        'logging': {
            'plot_bs': 5, 
            'visual_freq': 250, 
            'save_freq': 4_000_000, 
            'wandb_project': "torch-interpolants",
            'wandb_name': "cifar10-experiment",
            'wandb_entity': wandb_entity,
            'output_folder': output_folder,
            'output_name': "cifar10-experiment",
        },
        
        'network': {
            'network_type': "edm2",
            'load_path': "",
            'reset_optimizer': False,
            'img_resolution': 32,
            'img_channels': 3,
            'input_dims': (3, 32, 32),
            'label_dim': 0, 
            'use_cfg': False,
            'logvar_channels': 128,
            'use_bfloat16': False,
            'unet_kwargs': {
                'model_channels': 128,
                'channel_mult': [2, 2, 2], 
                'num_blocks': 3, 
                'attn_resolutions': [16, 8],
                'use_fourier': False, 
                'block_kwargs': {'dropout': 0.13}, 
            },
        },
    }
    
    # Update total samples
    config['optimization']['total_samples'] = (
        config['optimization']['bs'] * config['optimization']['total_steps']
    )
    
    # Update label_dim for conditional training
    if config['training']['conditional']:
        config['network']['label_dim'] = config['problem']['num_classes']
    
    # Set output_name dynamically from wandb_name
    config['logging']['output_name'] = config['logging']['wandb_name']
    
    return config


def get_cifar10_conditional_config(
    dataset_location: str = "./data",
    output_folder: str = "./outputs",
    wandb_entity: str = None,
) -> Dict[str, Any]:
    """Get conditional CIFAR-10 configuration."""
    
    config = get_cifar10_config(dataset_location, output_folder, wandb_entity)
    
    # Enable conditional training
    config['training']['conditional'] = True
    config['network']['label_dim'] = config['problem']['num_classes']
    config['network']['use_cfg'] = True
    config['logging']['wandb_name'] = "cifar10-conditional-experiment"
    config['logging']['output_name'] = "cifar10-conditional-experiment"
    
    return config


def get_cifar10_debug_config(
    dataset_location: str = "./data",
    output_folder: str = "./outputs",
    wandb_entity: str = None,
) -> Dict[str, Any]:
    """Get CIFAR-10 debug configuration with reduced training time."""
    
    config = get_cifar10_config(dataset_location, output_folder, wandb_entity)
    
    # Reduce training for debugging
    config['optimization']['total_steps'] = 50000
    config['optimization']['bs'] = 64
    config['logging']['save_freq'] = 5000
    config['logging']['visual_freq'] = 500
    config['logging']['wandb_name'] = "cifar10-debug"
    config['logging']['output_name'] = "cifar10-debug"
    
    # Smaller network
    config['network']['unet_kwargs']['model_channels'] = 64
    config['network']['unet_kwargs']['channel_mult'] = [1, 2, 2]
    config['network']['unet_kwargs']['num_blocks'] = 2
    config['network']['unet_kwargs']['attn_resolutions'] = [8]
    
    return config


def get_cifar10_large_config(
    dataset_location: str = "./data",
    output_folder: str = "./outputs",
    wandb_entity: str = None,
) -> Dict[str, Any]:
    """Get large CIFAR-10 configuration for high-quality results."""
    
    config = get_cifar10_config(dataset_location, output_folder, wandb_entity)
    
    # Increase model capacity
    config['network']['unet_kwargs']['model_channels'] = 192
    config['network']['unet_kwargs']['channel_mult'] = [1, 2, 3, 4]
    config['network']['unet_kwargs']['num_blocks'] = 4
    config['network']['unet_kwargs']['attn_resolutions'] = [16, 8]
    
    # Longer training
    config['optimization']['total_steps'] = 2_000_000
    config['optimization']['learning_rate'] = 1e-4
    
    # More frequent logging for long training
    config['logging']['save_freq'] = 50000
    config['logging']['visual_freq'] = 2000
    config['logging']['wandb_name'] = "cifar10-large-experiment"
    config['logging']['output_name'] = "cifar10-large-experiment"
    
    return config
