"""
MNIST configuration for PyTorch interpolants.

Translation of jax-interpolants MNIST config.
"""

from typing import Dict, Any


def get_mnist_config(
    dataset_location: str = "./data",
    output_folder: str = "./outputs",
    wandb_entity: str = None,
) -> Dict[str, Any]:
    """Get MNIST configuration."""
    
    config = {
        # Training config
        'training': {
            'shuffle': True,
            'conditional': False,
            'loss_type': "velocity",
            'class_dropout': 0.0,
            'tmin': 0.0,
            'tmax': 1.0,
            'seed': 42,
            'ema_facs': [0.999, 0.9999],
        },
        
        # Problem config
        'problem': {
            'n': 60000,
            'd': 784,
            'target': "mnist",
            'image_dims': (1, 28, 28),
            'num_classes': 10,
            'dataset_location': dataset_location,
            'interp_type': "linear",
            'base': "gaussian",
            'gaussian_scale': "adaptive",
        },
        
        # Optimization config
        'optimization': {
            'bs': 128,
            'learning_rate': 1e-3,
            'clip': 5.0,
            'total_steps': 500_000,
            'schedule_type': "cosine",
            'decay_steps': 500_000,  
        },
        
        # Logging config
        'logging': {
            'plot_bs': 25000,
            'visual_freq': 100,
            'save_freq': 10000, # This correctly matches 500_000 // 50
            'wandb_project': "jax-interpolants-debug",
            'wandb_name': "mnist-experiment",
            'wandb_entity': wandb_entity,
            'output_folder': output_folder,
            'output_name': "mnist-experiment",
        },
        
        # Network config
        'network': {
            'network_type': "edm2",
            'load_path': "",
            'reset_optimizer': False,
            'img_resolution': 28,
            'img_channels': 1,
            'input_dims': (1, 28, 28),
            'label_dim': 0, 
            'use_cfg': False,
            'logvar_channels': 64,
            'use_bfloat16': False,
            'unet_kwargs': {
                'model_channels': 64,
                'channel_mult': [1, 2, 4],
                'num_blocks': 3,
                'attn_resolutions': [7],
                'use_fourier': True,
                'fourier_bandwidth': 10.0,
            },
        },
    }
    
    # Update total samples
    config['optimization']['total_samples'] = (
        config['optimization']['bs'] * config['optimization']['total_steps']
    )
    
    # Update label_dim for conditional training
    if config['training']['conditional']:
        config['network']['label_dim'] = config['problem']['num_classes']
        
    # Dynamically set output_name to match wandb_name
    config['logging']['output_name'] = config['logging']['wandb_name']
    
    return config

def get_mnist_conditional_config(
    dataset_location: str = "./data",
    output_folder: str = "./outputs",
    wandb_entity: str = None,
) -> Dict[str, Any]:
    """Get conditional MNIST configuration."""
    
    config = get_mnist_config(dataset_location, output_folder, wandb_entity)
    
    # Enable conditional training
    config['training']['conditional'] = True
    config['network']['label_dim'] = config['problem']['num_classes']
    config['network']['use_cfg'] = True
    config['logging']['wandb_name'] = "mnist-conditional-experiment"
    config['logging']['output_name'] = "mnist-conditional-experiment"
    
    return config


def get_mnist_debug_config(
    dataset_location: str = "./data",
    output_folder: str = "./outputs",
    wandb_entity: str = None,
) -> Dict[str, Any]:
    """Get MNIST debug configuration with reduced training time."""
    
    config = get_mnist_config(dataset_location, output_folder, wandb_entity)
    
    # Reduce training for debugging
    config['optimization']['total_steps'] = 10000
    config['optimization']['bs'] = 64
    config['logging']['save_freq'] = 1000
    config['logging']['visual_freq'] = 50
    config['logging']['wandb_name'] = "mnist-debug"
    config['logging']['output_name'] = "mnist-debug"
    
    # Smaller network
    config['network']['unet_kwargs']['model_channels'] = 32
    config['network']['unet_kwargs']['channel_mult'] = [1, 2]
    config['network']['unet_kwargs']['num_blocks'] = 2
    
    return config
