_wandb:
    value:
        cli_version: 0.19.1
        m: []
        python_version: 3.12.7
        t:
            "1":
                - 1
                - 41
                - 55
                - 105
            "2":
                - 1
                - 41
                - 55
                - 105
            "3":
                - 1
                - 13
                - 16
                - 23
                - 55
                - 61
            "4": 3.12.7
            "5": 0.19.1
            "8":
                - 5
            "12": 0.19.1
            "13": linux-x86_64
logging:
    value:
        output_folder: ./outputs
        output_name: mnist-experiment
        plot_bs: 25000
        save_freq: 10000
        visual_freq: 100
        wandb_entity: null
        wandb_name: mnist-experiment
        wandb_project: jax-interpolants-debug
network:
    value:
        img_channels: 1
        img_resolution: 28
        input_dims:
            - 1
            - 28
            - 28
        label_dim: 0
        load_path: ""
        logvar_channels: 64
        network_type: edm2
        reset_optimizer: false
        unet_kwargs:
            attn_resolutions:
                - 7
            channel_mult:
                - 1
                - 2
                - 4
            fourier_bandwidth: 10
            model_channels: 64
            num_blocks: 3
            use_fourier: true
        use_bfloat16: false
        use_cfg: false
optimization:
    value:
        bs: 128
        clip: 5
        decay_steps: 500000
        learning_rate: 0.001
        schedule_type: cosine
        total_samples: 64000000
        total_steps: 500000
problem:
    value:
        base: gaussian
        d: 784
        dataset_location: ./data
        gaussian_scale: adaptive
        image_dims:
            - 1
            - 28
            - 28
        interp_type: linear
        "n": 60000
        num_classes: 10
        target: mnist
training:
    value:
        class_dropout: 0
        conditional: false
        ema_facs:
            - 0.999
            - 0.9999
        loss_type: velocity
        seed: 42
        shuffle: true
        tmax: 1
        tmin: 0
