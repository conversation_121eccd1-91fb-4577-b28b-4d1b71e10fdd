{"os": "Linux-5.15.0-91-generic-x86_64-with-glibc2.31", "python": "CPython 3.12.7", "startedAt": "2025-07-31T19:34:16.858502Z", "args": ["--config-type", "default", "--use-wandb"], "program": "/mnt/newhome/anaconda3/bin/train-mnist", "git": {"remote": "https://Kasra<PERSON>bi:@github.com/Kasraarabi/torch-interpolants.git", "commit": "2bca0caac203d409010c8d6222d7f14b33c45de1"}, "email": "kasra<PERSON><PERSON>@gmail.com", "root": "/mnt/newhome/kasra/torch-interpolants", "host": "gpu3", "executable": "/mnt/newhome/anaconda3/bin/python", "cpu_count": 128, "cpu_count_logical": 256, "gpu": "NVIDIA RTX A6000", "gpu_count": 4, "disk": {"/": {"total": "200449576960", "used": "85204090880"}}, "memory": {"total": "540821827584"}, "cpu": {"count": 128, "countLogical": 256}, "gpu_nvidia": [{"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere"}, {"name": "NVIDIA RTX A5000", "memoryTotal": "25757220864", "cudaCores": 8192, "architecture": "Ampere"}, {"name": "NVIDIA RTX A5000", "memoryTotal": "25757220864", "cudaCores": 8192, "architecture": "Ampere"}], "cudaVersion": "12.2"}