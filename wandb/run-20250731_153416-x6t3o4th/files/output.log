2025-07-31 15:34:17,494 - torch_interpolants.experiments.train_mnist - INFO - Starting training...
2025-07-31 15:34:17,494 - torch_interpolants.training.trainer - INFO - Starting training for 1069 epochs (500000 total steps).
Epoch 0:  21%|███████████████████▎                                                                       | 99/468 [00:16<00:54,  6.77it/s, loss=993.3050, step=100]2025-07-31 15:34:33,670 - torch_interpolants.training.trainer - INFO - Step 100: loss: 977.4442, grad_norm: 31117.1992, lr: 0.0010
Epoch 0:  32%|█████████████████████████████▏                                                            | 152/468 [00:24<00:50,  6.32it/s, loss=989.9094, step=152]
Traceback (most recent call last):
  File "/mnt/newhome/anaconda3/bin/train-mnist", line 8, in <module>
    sys.exit(main())
             ^^^^^^
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/experiments/train_mnist.py", line 174, in main
    train_mnist(
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/experiments/train_mnist.py", line 132, in train_mnist
    trainer.train(train_dataloader)
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/training/trainer.py", line 213, in train
    epoch_metrics = self.train_epoch(dataloader)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/training/trainer.py", line 162, in train_epoch
    metrics = self.train_step(batch)
              ^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/training/trainer.py", line 144, in train_step
    project_to_sphere(self.model)
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/training/edm2_torch.py", line 32, in project_to_sphere
    norm_size = torch.tensor(norm.numel(), device=param.device)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
KeyboardInterrupt
