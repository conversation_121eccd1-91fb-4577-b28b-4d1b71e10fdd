2025-07-31 15:27:48,892 - torch_interpolants.experiments.train_mnist - INFO - Starting training...
2025-07-31 15:27:48,893 - torch_interpolants.training.trainer - INFO - Starting training for 1069 epochs (500000 total steps).
Epoch 0:   0%|                                                                                     | 0/468 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "/mnt/newhome/anaconda3/bin/train-mnist", line 8, in <module>
    sys.exit(main())
             ^^^^^^
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/experiments/train_mnist.py", line 174, in main
    train_mnist(
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/experiments/train_mnist.py", line 132, in train_mnist
    trainer.train(train_dataloader)
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/training/trainer.py", line 213, in train
    epoch_metrics = self.train_epoch(dataloader)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/training/trainer.py", line 162, in train_epoch
    metrics = self.train_step(batch)
              ^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/training/trainer.py", line 121, in train_step
    loss = self.loss_fn(self.model, x0, x1, labels, t)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/losses.py", line 62, in loss_fn
    return empirical_loss(
           ^^^^^^^^^^^^^^^
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/losses.py", line 29, in empirical_loss
    bt_batch = model(It_batch, t_batch, label_batch)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/newhome/anaconda3/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/newhome/anaconda3/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1857, in _call_impl
    return inner()
           ^^^^^^^
  File "/mnt/newhome/anaconda3/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1805, in inner
    result = forward_call(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/networks/edm2_net.py", line 522, in forward
    output = c_out * self.unet(xs_in, ts, class_labels, train=train).float()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/newhome/anaconda3/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/newhome/anaconda3/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/networks/edm2_net.py", line 422, in forward
    x = torch.cat([x, torch.ones_like(x[:, :1])], dim=1)
                                      ~^^^^^^^
IndexError: too many indices for tensor of dimension 1
