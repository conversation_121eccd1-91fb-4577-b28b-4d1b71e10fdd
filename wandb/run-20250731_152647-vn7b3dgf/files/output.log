2025-07-31 15:26:48,172 - torch_interpolants.experiments.train_mnist - INFO - Starting training...
2025-07-31 15:26:48,172 - torch_interpolants.training.trainer - INFO - Starting training for 1069 epochs (500000 total steps).
Epoch 0:   0%|                                                                                     | 0/468 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "/mnt/newhome/anaconda3/bin/train-mnist", line 8, in <module>
    sys.exit(main())
             ^^^^^^
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/experiments/train_mnist.py", line 174, in main
    train_mnist(
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/experiments/train_mnist.py", line 132, in train_mnist
    trainer.train(train_dataloader)
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/training/trainer.py", line 213, in train
    epoch_metrics = self.train_epoch(dataloader)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/training/trainer.py", line 162, in train_epoch
    metrics = self.train_step(batch)
              ^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/training/trainer.py", line 121, in train_step
    loss = self.loss_fn(self.model, x0, x1, labels, t)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/losses.py", line 69, in loss_fn
    return empirical_loss(
           ^^^^^^^^^^^^^^^
  File "/mnt/newhome/kasra/torch-interpolants/torch_interpolants/losses.py", line 26, in empirical_loss
    It_batch = interp.batch_calc_It(t_batch, x0_batch, x1_batch)
               ^^^^^^^^^^^^^^^^^^^^
  File "/mnt/newhome/anaconda3/lib/python3.12/site-packages/torch/nn/modules/module.py", line 1940, in __getattr__
    raise AttributeError(
AttributeError: 'Interpolant' object has no attribute 'batch_calc_It'
