pyct==0.5.0
pycryptodome==3.23.0
clip==1.0
appdirs==1.4.4
yarg==0.1.9
stable_baselines3==2.6.0
cookiecutter==2.6.0
GitPython==3.1.43
Werkzeug==3.0.3
sip==6.7.12
certifi==2025.7.14
docopt==0.6.2
executing==0.8.3
shellescape==3.8.1
boltons==23.0.0
libmambapy==1.5.8
docstring-to-markdown==0.11
einops==0.8.0
uritemplate==4.2.0
blinker==1.6.2
Flask==3.0.3
pytoolconfig==1.2.6
onnx==1.18.0
pylint==2.16.2
attrdict==2.0.1
google-auth-httplib2==0.2.0
fonttools==4.55.3
pytest==7.4.4
numpydoc==1.7.0
anaconda-anon-usage==0.4.4
pynndescent==0.5.13
triton==3.3.1
whatthepatch==1.0.2
scikit-learn==1.6.0
pycparser==2.21
bokeh==3.6.0
rsa==4.9.1
chardet==3.0.4
decorator==5.1.1
pycosat==0.6.6
beautifulsoup4==4.12.3
httpcore==0.9.1
grpcio-status==1.71.2
ipython-genutils==0.2.0
pyOpenSSL==24.2.1
adabelief-pytorch==0.2.0
pyviz_comms==3.0.2
diffusers==0.33.1
sphinxcontrib-jsmath==1.0.1
cloudpickle==3.0.0
wandb==0.19.1
googletrans==4.0.0rc1
albucore==0.0.24
annotated-types==0.7.0
patsy==0.5.6
requests-toolbelt==1.0.0
textdistance==4.2.1
mpmath==1.3.0
xformers==0.0.31.post1
tifffile==2024.12.12
datasets==3.2.0
lz4==4.3.2
orjson==3.11.0
mdit-py-plugins==0.3.0
typing_extensions==4.14.1
requests-file==1.5.1
python-slugify==5.0.2
ruamel-yaml-conda==0.17.21
distro==1.9.0
referencing==0.30.2
Twisted==23.10.0
fake-useragent==2.2.0
simsimd==6.5.0
partd==1.4.1
gym-notices==0.0.8
flake8==7.0.0
parsel==1.8.1
semver==3.0.2
torch-geometric==2.6.1
prompt-toolkit==3.0.43
atomicwrites==1.4.0
pluggy==1.0.0
colorcet==3.1.0
transformers==4.53.2
duckduckgo_search==8.1.1
courlan==1.3.2
conda_index==0.5.0
three-merge==0.1.1
pytorch-lightning==2.5.2
hvplot==0.11.0
pydub==0.25.1
tzlocal==5.3.1
linkify-it-py==2.0.0
starlette==0.47.1
llvmlite==0.43.0
notebook_shim==0.2.3
urllib3==2.5.0
datashader==0.16.3
Brotli==1.0.9
aioitertools==0.7.1
psutil==6.1.1
terminado==0.17.1
charset-normalizer==3.4.0
soxr==0.5.0.post1
imbalanced-learn==0.12.3
nvidia-cuda-runtime-cu12==12.6.77
translate==3.6.1
termcolor==2.5.0
nbclient==0.8.0
pycodestyle==2.11.1
statsmodels==0.14.2
Scrapy==2.11.1
lxml==5.4.0
SQLAlchemy==2.0.34
Mako==1.3.10
conda-token==0.5.0+1.g2209e04
jupyterlab-pygments==0.1.2
lazy-object-proxy==1.10.0
pydocstyle==6.3.0
click==8.2.1
dateparser==1.2.2
coloredlogs==15.0.1
torch==2.7.1
tblib==1.7.0
ipython==8.12.3
PyYAML==6.0.2
PyQt5-sip==12.13.0
python-json-logger==2.0.7
imagesize==1.4.1
docker-pycreds==0.4.0
scikit-optimize==0.10.2
mdurl==0.1.0
conda-libmamba-solver==24.9.0
sentence-transformers==3.3.1
Cython==3.1.2
rich==13.7.1
snowballstemmer==2.2.0
Shimmy==2.0.0
networkx==3.4.2
conda-content-trust==0.2.0
nvidia-cudnn-cu11==********
itemadapter==0.3.0
soundfile==0.13.1
nvidia-nvjitlink-cu12==12.6.85
pyparsing==3.2.0
colorlog==6.9.0
httpx==0.13.3
galois==0.4.6
nvidia-cuda-cupti-cu12==12.6.80
ipywidgets==7.8.1
nvidia-cudnn-cu12==********
hyperframe==5.2.0
conda==24.9.2
Markdown==3.4.1
anaconda-catalogs==0.2.0
python-lsp-jsonrpc==1.1.2
anaconda-navigator==2.6.3
uc-micro-py==1.0.1
Automat==20.2.0
nest-asyncio==1.6.0
smart-open==5.2.1
pyee==13.0.0
conda_package_streaming==0.10.0
inflection==0.5.1
ujson==5.10.0
tokenizers==0.21.0
PyDispatcher==2.0.5
libretranslatepy==2.1.1
gymnasium==1.1.1
mkl_random==1.2.7
wheel==0.45.1
safetensors==0.4.5
idna==2.10
pyxdg==0.27
nbconvert==7.16.4
prettytable==3.12.0
prometheus-client==0.14.1
conda-build==24.9.0
anaconda-client==1.12.3
astropy-iers-data==0.2024.********.23
python-multipart==0.0.20
jsonpatch==1.33
rfc3986-validator==0.1.1
soupsieve==2.5
pyarrow==18.1.0
tensorboard==2.19.0
pooch==1.8.2
Unidecode==1.3.8
pyls-spyder==0.4.0
reedsolo==1.7.0
nvidia-nvtx-cu12==12.6.77
msgpack==1.0.3
pydantic==2.10.4
googleapis-common-protos==1.70.0
jupyter==1.0.0
truststore==0.8.0
pydeck==0.8.0
mypy-extensions==1.0.0
playwright==1.54.0
nvidia-cublas-cu11==**********
pycurl==7.45.3
grpcio==1.73.1
jupyter_server==2.14.1
h2==3.2.0
panel==1.5.2
Protego==0.1.16
frozenlist==1.5.0
wcwidth==0.2.13
insightface==0.7.3
PyJWT==2.8.0
yapf==0.40.2
Rtree==1.0.1
bm4d==4.2.5
nvidia-curand-cu12==*********
jupyter_core==5.7.2
jupyter-lsp==2.2.0
cffi==1.17.1
onnxruntime==1.22.1
packaging==24.2
webdataset==1.0.2
aiosignal==1.3.2
python-etcd==0.4.5
compressai==1.2.6
botocore==1.34.69
rope==1.12.0
binaryornot==0.4.4
pillow==10.4.0
pypinyin==0.50.0
Sphinx==7.3.7
absl-py==2.2.2
bleach==4.1.0
PyMatching==2.2.2
multipledispatch==0.6.0
w3lib==1.21.0
s3fs==2024.6.1
jsonschema-specifications==2023.7.1
gensim==4.3.3
QtPy==2.4.1
antlr4-python3-runtime==4.9.3
imageio==2.36.1
nvidia-cufft-cu12==********
google-ai-generativelanguage==0.6.15
propcache==0.2.1
six==1.17.0
jusText==3.0.2
Jinja2==3.1.4
nvidia-cuda-runtime-cu11==11.7.99
pyflakes==3.2.0
aiobotocore==2.12.3
kiwisolver==1.4.7
debugpy==1.6.7
argon2-cffi==21.3.0
crypto==1.4.1
qstylizer==0.2.2
xarray==2023.6.0
rouge_score==0.1.2
lark==1.1.9
locket==1.0.0
webencodings==0.5.1
humanfriendly==10.0
spyder-kernels==2.5.0
MarkupSafe==2.1.5
kagglehub==0.3.12
multiprocess==0.70.16
py-cpuinfo==9.0.0
jaraco.classes==3.2.1
setproctitle==1.3.4
jupyter-console==6.6.3
dask==2024.8.2
aiohttp==3.11.11
nvidia-nccl-cu12==2.26.2
requests==2.32.3
service-identity==18.1.0
et-xmlfile==1.1.0
dask-expr==1.1.13
anaconda-cloud-auth==0.5.1
itsdangerous==2.2.0
notebook==7.2.2
arrow==1.2.3
rfc3339-validator==0.1.4
aiofiles==23.2.1
filelock==3.16.1
frozendict==2.4.2
tenacity==8.2.3
hf-xet==1.1.5
HeapDict==1.0.1
hyperlink==21.0.0
streamlit==1.37.1
primp==0.15.0
lazy_loader==0.4
navigator-updater==0.5.1
stringzilla==3.12.5
astunparse==1.6.3
tensorboard-data-server==0.7.2
libarchive-c==5.1
pure-eval==0.2.2
google-auth==2.40.3
dill==0.3.8
jmespath==1.0.1
setuptools==80.9.0
torchinfo==1.8.0
loguru==0.7.3
isort==5.13.2
platformdirs==4.3.6
pyzmq==25.1.2
nvidia-cusparselt-cu12==0.6.3
widgetsnbextension==3.6.6
python-dotenv==0.21.0
sphinxcontrib-htmlhelp==2.0.0
expecttest==0.2.1
ldpc==2.3.6
sympy==1.14.0
easydict==1.13
numba==0.60.0
incremental==22.10.0
outcome==1.3.0.post0
importlib_resources==6.5.2
nvidia-cusolver-cu12==********
diff-match-patch==20200713
openai==0.28.0
pip==25.1.1
argon2-cffi-bindings==21.2.0
sentry-sdk==2.19.2
tabulate==0.9.0
huggingface-hub==0.33.2
timm==1.0.15
nvidia-cublas-cu12==********
latex2mathml==3.78.0
conda-repo-cli==1.0.114
tqdm==4.64.0
stim==1.15.0
nvidia-cufile-cu12==********
nvidia-cuda-nvrtc-cu11==11.7.99
lmdb==1.4.1
PySocks==1.7.1
fsspec==2024.9.0
omegaconf==2.3.0
attrs==24.3.0
exceptiongroup==1.2.2
jupyterlab_server==2.27.3
importlib_metadata==8.5.0
dnspython==2.6.1
regex==2024.11.6
python-dateutil==2.9.0.post0
cssselect==1.2.0
uvicorn==0.35.0
astroid==2.14.2
conda-package-handling==2.3.0
sentencepiece==0.1.96
docutils==0.18.1
gym==0.26.2
cytoolz==0.12.2
matplotlib==3.10.0
zipp==3.21.0
spyder==5.5.1
anaconda-project==0.11.1
cryptography==43.0.0
xxhash==3.5.0
archspec==0.2.3
joblib==1.4.2
open_clip_torch==2.32.0
param==2.1.1
Farama-Notifications==0.0.4
python-lsp-server==1.10.0
greenlet==3.2.3
PyWavelets==1.8.0
torch-interpolants==0.1.0
intake==2.0.7
x-transformers==1.29.2
google-generativeai==0.8.5
optuna==4.4.0
clean-fid==0.1.35
rfc3986==1.5.0
sphinxcontrib-qthelp==1.0.3
opencv-python-headless==*********
holoviews==1.19.1
QDarkStyle==3.2.3
fastjsonschema==2.16.2
cma==4.2.0
threadpoolctl==3.5.0
numexpr==2.8.7
zstandard==0.23.0
pylint-venv==3.0.3
pyaml==25.5.0
ftfy==6.3.1
black==24.8.0
keyring==24.3.1
portalocker==3.2.0
contourpy==1.3.1
nvitop==1.3.2
Pygments==2.12.0
toolz==0.12.0
mypy==1.11.2
scipy==1.14.1
distributed==2024.8.2
yarl==1.18.3
hpack==3.0.0
queuelib==1.6.2
gitdb==4.0.11
braceexpand==0.1.7
Bottleneck==1.3.7
htmldate==1.9.3
pkginfo==1.10.0
ml_collections==1.1.0
nbformat==5.10.4
pyasn1-modules==0.2.8
bcrypt==3.2.0
wurlitzer==3.0.2
lxml_html_clean==0.4.2
jupyter_client==8.6.0
babel==2.17.0
tomli==2.0.1
google-api-core==2.25.1
PyQtWebEngine==5.15.6
PyQt5==5.15.10
Naked==0.1.32
nvidia-cuda-nvrtc-cu12==12.6.77
mkl_fft==1.3.10
qtconsole==5.5.1
conda-pack==0.7.1
pandocfilters==1.5.0
toml==0.10.2
pipreqs==0.5.0
imagecodecs==2023.1.23
jellyfish==1.0.1
google-lens-python==2023.3.18
google-reverse-search==0.1.3
openpyxl==3.1.5
xyzservices==2022.9.0
pytorch-msssim==1.0.0
tld==0.13.1
constantly==23.10.4
traitlets==5.14.3
matplotlib-inline==0.1.6
jupyter-events==0.10.0
jedi==0.19.1
mistune==2.0.4
torchmetrics==1.7.1
comm==0.2.1
trio-websocket==0.12.2
markdown-it-py==2.2.0
undetected-chromedriver==3.5.5
websockets==11.0.3
alembic==1.16.4
h5py==3.11.0
selenium==4.34.2
uv==0.8.0
pyasn1==0.4.8
pexpect==4.8.0
pyerfa==*******
bs4==0.0.2
jupyterlab-widgets==1.0.0
pytz==2024.2
sortedcontainers==2.4.0
tzdata==2024.2
intervaltree==3.1.0
altair==5.0.1
pathspec==0.10.3
sniffio==1.3.1
plotly==5.24.1
backcall==0.2.0
lightning-utilities==0.11.9
jsonschema==4.23.0
umap-learn==0.5.7
ply==3.11
optree==0.11.0
aiohappyeyeballs==2.4.4
flatbuffers==25.2.10
wrapt==1.14.1
torchvision==0.22.1
unicodedata2==15.1.0
nvidia-ml-py==12.535.161
menuinst==2.1.2
hstspreload==2025.1.1
asttokens==2.0.5
google-api-python-client==2.177.0
sinter==1.15.0
numpy==1.26.4
jeepney==0.7.1
ollama==0.4.4
mdtex2html==1.3.0
semantic-version==2.10.0
trafilatura==2.0.0
rpds-py==0.10.6
iniconfig==1.1.1
lckr_jupyterlab_variableinspector==3.1.0
nltk==3.9.1
nvidia-cusparse-cu12==12.5.4.2
pyodbc==5.1.0
librosa==0.11.0
defusedxml==0.7.1
itemloaders==1.1.0
seaborn==0.13.2
colorama==0.4.5
opencv-python==*********
sphinxcontrib-serializinghtml==1.1.10
playwright-stealth==1.0.2
tables==3.10.1
parso==0.8.3
text-unidecode==1.3
accelerate==1.2.1
astropy==6.1.3
scikit-image==0.25.0
albumentations==2.0.8
ptyprocess==0.7.0
anyio==4.7.0
SecretStorage==3.3.1
gradio_client==0.6.1
fastapi==0.116.1
async-lru==2.0.4
sacrebleu==2.5.1
tornado==6.4.1
h11==0.9.0
httplib2==0.22.0
bchlib==2.1.3
QtAwesome==1.3.1
google-cloud-vision==3.10.2
ipykernel==6.28.0
ruamel.yaml==0.18.6
trio==0.30.0
jsonpointer==2.1
alabaster==0.7.16
jupyterlab==4.2.5
torch-fidelity==0.3.0
tldextract==5.1.2
async-timeout==5.0.1
tiktoken==0.5.2
proto-plus==1.26.1
autopep8==2.0.4
cachetools==5.5.0
ruamel.yaml.clib==0.2.8
sphinxcontrib-devhelp==1.0.2
multidict==6.1.0
flask-cors==6.0.1
pkce==1.0.3
lpips==0.1.4
pickleshare==0.7.5
bert-score==0.3.13
more-itertools==10.3.0
hypothesis==6.103.0
protobuf==5.29.5
zope.interface==5.4.0
mkl-service==2.4.0
pydantic_core==2.27.2
ffmpy==0.6.0
stack-data==0.2.0
audioread==3.0.1
tomlkit==0.11.1
cycler==0.12.1
webdriver-manager==4.0.2
types-dataclasses==0.6.6
mccabe==0.7.0
janus==1.0.0
wsproto==1.2.0
ddgs==9.4.3
Send2Trash==1.8.2
json5==0.9.6
pandas==2.2.3
google_search_results==2.4.2
zict==3.0.0
jupyter_server_terminals==0.4.4
sphinxcontrib-applehelp==1.0.2
python-lsp-black==2.0.0
watchdog==4.0.1
gradio==3.48.0
websocket-client==1.8.0
smmap==5.0.1
bm3d==4.0.3
tinycss2==1.2.1
overrides==7.4.0
