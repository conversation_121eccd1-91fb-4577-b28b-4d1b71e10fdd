2025-07-31 15:31:37,355 INFO    MainThread:3121176 [wandb_setup.py:_flush():68] Current SDK version is 0.19.1
2025-07-31 15:31:37,355 INFO    MainThread:3121176 [wandb_setup.py:_flush():68] Configure stats pid to 3121176
2025-07-31 15:31:37,355 INFO    MainThread:3121176 [wandb_setup.py:_flush():68] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-31 15:31:37,355 INFO    MainThread:3121176 [wandb_setup.py:_flush():68] Loading settings from /mnt/newhome/kasra/torch-interpolants/wandb/settings
2025-07-31 15:31:37,355 INFO    MainThread:3121176 [wandb_setup.py:_flush():68] Loading settings from environment variables
2025-07-31 15:31:37,355 INFO    MainThread:3121176 [wandb_init.py:_log_setup():528] Logging user logs to /mnt/newhome/kasra/torch-interpolants/wandb/run-20250731_153137-g4hd3ohn/logs/debug.log
2025-07-31 15:31:37,355 INFO    MainThread:3121176 [wandb_init.py:_log_setup():529] Logging internal logs to /mnt/newhome/kasra/torch-interpolants/wandb/run-20250731_153137-g4hd3ohn/logs/debug-internal.log
2025-07-31 15:31:37,355 INFO    MainThread:3121176 [wandb_init.py:init():644] calling init triggers
2025-07-31 15:31:37,355 INFO    MainThread:3121176 [wandb_init.py:init():650] wandb.init called with sweep_config: {}
config: {'training': {'shuffle': True, 'conditional': False, 'loss_type': 'velocity', 'class_dropout': 0.0, 'tmin': 0.0, 'tmax': 1.0, 'seed': 42, 'ema_facs': [0.999, 0.9999]}, 'problem': {'n': 60000, 'd': 784, 'target': 'mnist', 'image_dims': (1, 28, 28), 'num_classes': 10, 'dataset_location': './data', 'interp_type': 'linear', 'base': 'gaussian', 'gaussian_scale': 'adaptive'}, 'optimization': {'bs': 128, 'learning_rate': 0.001, 'clip': 5.0, 'total_steps': 500000, 'schedule_type': 'cosine', 'decay_steps': 500000, 'total_samples': 64000000}, 'logging': {'plot_bs': 25000, 'visual_freq': 100, 'save_freq': 10000, 'wandb_project': 'jax-interpolants-debug', 'wandb_name': 'mnist-experiment', 'wandb_entity': None, 'output_folder': './outputs', 'output_name': 'mnist-experiment'}, 'network': {'network_type': 'edm2', 'load_path': '', 'reset_optimizer': False, 'img_resolution': 28, 'img_channels': 1, 'input_dims': (1, 28, 28), 'label_dim': 0, 'use_cfg': False, 'logvar_channels': 64, 'use_bfloat16': False, 'unet_kwargs': {'model_channels': 64, 'channel_mult': [1, 2, 4], 'num_blocks': 3, 'attn_resolutions': [7], 'use_fourier': True, 'fourier_bandwidth': 10.0}}}
2025-07-31 15:31:37,356 INFO    MainThread:3121176 [wandb_init.py:init():680] starting backend
2025-07-31 15:31:37,356 INFO    MainThread:3121176 [wandb_init.py:init():684] sending inform_init request
2025-07-31 15:31:37,360 INFO    MainThread:3121176 [backend.py:_multiprocessing_setup():104] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-07-31 15:31:37,361 INFO    MainThread:3121176 [wandb_init.py:init():697] backend started and connected
2025-07-31 15:31:37,362 INFO    MainThread:3121176 [wandb_init.py:init():790] updated telemetry
2025-07-31 15:31:37,365 INFO    MainThread:3121176 [wandb_init.py:init():822] communicating run to backend with 90.0 second timeout
2025-07-31 15:31:37,703 INFO    MainThread:3121176 [wandb_init.py:init():874] starting run threads in backend
2025-07-31 15:31:37,961 INFO    MainThread:3121176 [wandb_run.py:_console_start():2374] atexit reg
2025-07-31 15:31:37,961 INFO    MainThread:3121176 [wandb_run.py:_redirect():2224] redirect: wrap_raw
2025-07-31 15:31:37,961 INFO    MainThread:3121176 [wandb_run.py:_redirect():2289] Wrapping output streams.
2025-07-31 15:31:37,962 INFO    MainThread:3121176 [wandb_run.py:_redirect():2314] Redirects installed.
2025-07-31 15:31:37,963 INFO    MainThread:3121176 [wandb_init.py:init():916] run started, returning control to user process
2025-07-31 15:31:37,963 INFO    MainThread:3121176 [wandb_watch.py:_watch():71] Watching
2025-07-31 15:31:38,359 WARNING MsgRouterThr:3121176 [router.py:message_loop():75] message_loop has been closed
