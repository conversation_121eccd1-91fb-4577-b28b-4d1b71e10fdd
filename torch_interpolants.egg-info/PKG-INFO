Metadata-Version: 2.4
Name: torch-interpolants
Version: 0.1.0
Summary: PyTorch implementation of stochastic interpolants for generative modeling
Home-page: https://github.com/your-username/torch-interpolants
Author: PyTorch Interpolants Team
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: torch>=2.0.0
Requires-Dist: torchvision>=0.15.0
Requires-Dist: numpy>=1.21.0
Requires-Dist: tqdm>=4.64.0
Requires-Dist: matplotlib>=3.5.0
Requires-Dist: Pillow>=8.3.0
Requires-Dist: wandb>=0.13.0
Requires-Dist: torch-fidelity>=0.3.0
Requires-Dist: clean-fid>=0.1.35
Requires-Dist: pytest>=7.0.0
Requires-Dist: ml-collections>=0.1.1
Provides-Extra: jax
Requires-Dist: jax[cpu]>=0.4.0; extra == "jax"
Requires-Dist: flax>=0.6.0; extra == "jax"
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: black>=22.0.0; extra == "dev"
Requires-Dist: isort>=5.10.0; extra == "dev"
Dynamic: author
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# PyTorch Interpolants

A PyTorch implementation of stochastic interpolants for generative modeling, translated from the JAX-based [jax-interpolants](https://github.com/nmboffi/jax-interpolants) repository.

## Overview

This library provides a complete PyTorch implementation of the stochastic interpolant framework described in [Stochastic Interpolants: A Unifying Framework for Flows and Diffusions](https://arxiv.org/abs/2303.08797). The implementation maintains numerical equivalence with the original JAX version while being fully compatible with PyTorch training loops.

## Features

- **Complete interpolant framework**: Linear, trigonometric, and diffusion-based interpolants
- **EDM2 U-Net architecture**: High-performance neural network for image generation
- **Multiple sampling methods**: Euler and Heun integrators for ODE solving
- **Training utilities**: EMA, gradient clipping, learning rate scheduling
- **Evaluation tools**: FID score calculation and sample generation
- **Numerical equivalence**: Verified against JAX implementation within 1e-4 tolerance

### Requirements

- Python >= 3.8
- PyTorch >= 2.0.0
- torchvision >= 0.15.0
- Additional dependencies in `requirements.txt`

## Quick Start

### Training on MNIST

```python
from torch_interpolants.experiments.train_mnist import train_mnist

# Train with default configuration
train_mnist(
    config_type="default",
    dataset_location="./data",
    output_folder="./outputs",
    use_wandb=True
)
```

### Training on CIFAR-10

```python
from torch_interpolants.experiments.train_cifar10 import train_cifar10

# Train with debug configuration (faster)
train_cifar10(
    config_type="debug",
    dataset_location="./data",
    output_folder="./outputs",
    use_wandb=True
)
```

### Command Line Interface

```bash
# Train MNIST model
train-mnist --config-type debug --use-wandb

# Train CIFAR-10 model
train-cifar10 --config-type default --use-wandb

# Evaluate FID score
evaluate-fid checkpoint.pt --dataset cifar10 --num-samples 50000
```

## Usage Examples

### Basic Interpolant Usage

```python
import torch
from torch_interpolants import Interpolant, setup_interpolant

# Create configuration
config = {
    'problem': {
        'interp_type': 'linear'
    }
}

# Setup interpolant
interpolant = setup_interpolant(config)

# Sample data
x0 = torch.randn(32, 3, 32, 32)  # Base distribution
x1 = torch.randn(32, 3, 32, 32)  # Target distribution
t = torch.rand(32)               # Time points

# Compute interpolant
It = interpolant.batch_calc_It(t, x0, x1)
velocity = interpolant.batch_calc_target(t, x0, x1, "velocity")
```

### Custom Training Loop

```python
import torch
from torch_interpolants import InterpolantTrainer
from torch_interpolants.networks import PrecondUNet
from torch_interpolants.datasets import get_cifar10_dataloader

# Create model
model = PrecondUNet(
    img_resolution=32,
    img_channels=3,
    label_dim=0,
    model_channels=128,
    channel_mult=[1, 2, 2, 2],
    num_blocks=4,
)

# Create dataloader
dataloader = get_cifar10_dataloader(batch_size=128, train=True)

# Create trainer
trainer = InterpolantTrainer(
    model=model,
    interpolant=interpolant,
    config=config,
    use_wandb=True
)

# Train
trainer.train(dataloader)
```

### Sampling

```python
# Generate samples
samples = trainer.sample(
    num_samples=25,
    num_steps=50,
    method="heun"
)

# Save samples
import torchvision.utils as vutils
vutils.save_image(samples, "samples.png", nrow=5, normalize=True)
```

## Architecture

The library is organized into several modules:

- `interpolant.py`: Core interpolant implementations
- `samplers.py`: ODE/SDE sampling methods
- `losses.py`: Loss functions for training
- `networks/`: Neural network architectures (EDM2 U-Net)
- `training/`: Training utilities (EMA, optimizers, schedulers)
- `datasets/`: Dataset loaders for MNIST and CIFAR-10
- `configs/`: Configuration files
- `experiments/`: Training and evaluation scripts

## Interpolant Types

### Linear Interpolant
```python
I_t(x_0, x_1) = (1-t) * x_0 + t * x_1
```

### Trigonometric Interpolant
```python
I_t(x_0, x_1) = cos(πt/2) * x_0 + sin(πt/2) * x_1
```

### VP Diffusion Interpolant
```python
I_t(x_0, x_1) = √(1 - exp(2(t - t_max))) * x_0 + exp(t - t_max) * x_1
```

## Numerical Verification

The implementation has been verified for numerical equivalence with the original JAX version:

```bash
# Run equivalence tests
python -m pytest tests/test_interpolant_equivalence.py -v
```

Tests verify:
- Interpolant calculations match within 1e-4 tolerance
- Batch operations are consistent with single operations
- All interpolant types produce correct boundary conditions
- Target calculations (velocity, score, noise, denoiser) are accurate

## Configuration

The library uses hierarchical configurations similar to the JAX version:

```python
config = {
    'training': {
        'loss_type': 'velocity',
        'ema_facs': [0.999, 0.9999],
        'conditional': False,
    },
    'optimization': {
        'learning_rate': 1e-3,
        'total_steps': 500_000,
        'schedule_type': 'cosine',
    },
    'network': {
        'model_channels': 128,
        'channel_mult': [1, 2, 2, 2],
        'use_fourier': True,
    }
}
```
