"""
Setup script for torch-interpolants.
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="torch-interpolants",
    version="0.1.0",
    author="PyTorch Interpolants Team",
    description="PyTorch implementation of stochastic interpolants for generative modeling",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/torch-interpolants",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "jax": ["jax[cpu]>=0.4.0", "flax>=0.6.0"],
        "dev": ["pytest>=7.0.0", "black>=22.0.0", "isort>=5.10.0"],
    },
    entry_points={
        "console_scripts": [
            "train-mnist=torch_interpolants.experiments.train_mnist:main",
            "train-cifar10=torch_interpolants.experiments.train_cifar10:main",
            "evaluate-fid=torch_interpolants.experiments.evaluate_fid:main",
        ],
    },
)
