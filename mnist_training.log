2025-07-31 15:24:39,493 - torch_interpolants.experiments.train_mnist - INFO - Using config type: default
2025-07-31 15:24:39,494 - torch_interpolants.experiments.train_mnist - INFO - Training steps: 500000
2025-07-31 15:24:39,495 - torch_interpolants.experiments.train_mnist - INFO - Batch size: 128
2025-07-31 15:24:39,497 - torch_interpolants.experiments.train_mnist - INFO - Using interpolant type: linear
2025-07-31 15:26:45,127 - torch_interpolants.experiments.train_mnist - INFO - Using config type: default
2025-07-31 15:26:45,127 - torch_interpolants.experiments.train_mnist - INFO - Training steps: 500000
2025-07-31 15:26:45,127 - torch_interpolants.experiments.train_mnist - INFO - Batch size: 128
2025-07-31 15:26:45,127 - torch_interpolants.experiments.train_mnist - INFO - Using interpolant type: linear
2025-07-31 15:26:45,459 - torch_interpolants.experiments.train_mnist - INFO - Total parameters: 21,858,076
2025-07-31 15:26:45,459 - torch_interpolants.experiments.train_mnist - INFO - Trainable parameters: 21,858,076
2025-07-31 15:26:47,069 - torch_interpolants.experiments.train_mnist - INFO - Training dataset size: 60000
2025-07-31 15:26:48,172 - torch_interpolants.experiments.train_mnist - INFO - Starting training...
2025-07-31 15:26:48,172 - torch_interpolants.training.trainer - INFO - Starting training for 1069 epochs (500000 total steps).
2025-07-31 15:27:47,452 - torch_interpolants.experiments.train_mnist - INFO - Using config type: default
2025-07-31 15:27:47,452 - torch_interpolants.experiments.train_mnist - INFO - Training steps: 500000
2025-07-31 15:27:47,453 - torch_interpolants.experiments.train_mnist - INFO - Batch size: 128
2025-07-31 15:27:47,453 - torch_interpolants.experiments.train_mnist - INFO - Using interpolant type: linear
2025-07-31 15:27:47,774 - torch_interpolants.experiments.train_mnist - INFO - Total parameters: 21,858,076
2025-07-31 15:27:47,774 - torch_interpolants.experiments.train_mnist - INFO - Trainable parameters: 21,858,076
2025-07-31 15:27:47,945 - torch_interpolants.experiments.train_mnist - INFO - Training dataset size: 60000
2025-07-31 15:27:48,892 - torch_interpolants.experiments.train_mnist - INFO - Starting training...
2025-07-31 15:27:48,893 - torch_interpolants.training.trainer - INFO - Starting training for 1069 epochs (500000 total steps).
2025-07-31 15:31:36,445 - torch_interpolants.experiments.train_mnist - INFO - Using config type: default
2025-07-31 15:31:36,445 - torch_interpolants.experiments.train_mnist - INFO - Training steps: 500000
2025-07-31 15:31:36,445 - torch_interpolants.experiments.train_mnist - INFO - Batch size: 128
2025-07-31 15:31:36,446 - torch_interpolants.experiments.train_mnist - INFO - Using interpolant type: linear
2025-07-31 15:31:36,755 - torch_interpolants.experiments.train_mnist - INFO - Total parameters: 21,858,076
2025-07-31 15:31:36,755 - torch_interpolants.experiments.train_mnist - INFO - Trainable parameters: 21,858,076
2025-07-31 15:31:36,921 - torch_interpolants.experiments.train_mnist - INFO - Training dataset size: 60000
2025-07-31 15:31:37,965 - torch_interpolants.experiments.train_mnist - INFO - Starting training...
2025-07-31 15:31:37,965 - torch_interpolants.training.trainer - INFO - Starting training for 1069 epochs (500000 total steps).
2025-07-31 15:32:48,872 - torch_interpolants.experiments.train_mnist - INFO - Using config type: default
2025-07-31 15:32:48,872 - torch_interpolants.experiments.train_mnist - INFO - Training steps: 500000
2025-07-31 15:32:48,872 - torch_interpolants.experiments.train_mnist - INFO - Batch size: 128
2025-07-31 15:32:48,873 - torch_interpolants.experiments.train_mnist - INFO - Using interpolant type: linear
2025-07-31 15:32:49,198 - torch_interpolants.experiments.train_mnist - INFO - Total parameters: 21,858,076
2025-07-31 15:32:49,198 - torch_interpolants.experiments.train_mnist - INFO - Trainable parameters: 21,858,076
2025-07-31 15:32:49,368 - torch_interpolants.experiments.train_mnist - INFO - Training dataset size: 60000
2025-07-31 15:32:50,526 - torch_interpolants.experiments.train_mnist - INFO - Starting training...
2025-07-31 15:32:50,526 - torch_interpolants.training.trainer - INFO - Starting training for 1069 epochs (500000 total steps).
2025-07-31 15:34:15,935 - torch_interpolants.experiments.train_mnist - INFO - Using config type: default
2025-07-31 15:34:15,935 - torch_interpolants.experiments.train_mnist - INFO - Training steps: 500000
2025-07-31 15:34:15,935 - torch_interpolants.experiments.train_mnist - INFO - Batch size: 128
2025-07-31 15:34:15,935 - torch_interpolants.experiments.train_mnist - INFO - Using interpolant type: linear
2025-07-31 15:34:16,263 - torch_interpolants.experiments.train_mnist - INFO - Total parameters: 21,858,076
2025-07-31 15:34:16,263 - torch_interpolants.experiments.train_mnist - INFO - Trainable parameters: 21,858,076
2025-07-31 15:34:16,429 - torch_interpolants.experiments.train_mnist - INFO - Training dataset size: 60000
2025-07-31 15:34:17,494 - torch_interpolants.experiments.train_mnist - INFO - Starting training...
2025-07-31 15:34:17,494 - torch_interpolants.training.trainer - INFO - Starting training for 1069 epochs (500000 total steps).
2025-07-31 15:34:33,670 - torch_interpolants.training.trainer - INFO - Step 100: loss: 977.4442, grad_norm: 31117.1992, lr: 0.0010
