{"cells": [{"cell_type": "code", "execution_count": null, "id": "fd7a9d97", "metadata": {}, "outputs": [], "source": ["import torch\n", "import sys\n", "import matplotlib as mpl\n", "from matplotlib import pyplot as plt\n", "from torch import vmap\n", "import seaborn as sns\n", "import numpy as np\n", "from math import pi\n", "import time\n", "\n", "\n", "## nice defaults\n", "%matplotlib inline\n", "mpl.rcParams['axes.grid']  = True\n", "mpl.rcParams['axes.grid.which']  = 'both'\n", "mpl.rcParams['xtick.minor.visible']  = True\n", "mpl.rcParams['ytick.minor.visible']  = True\n", "mpl.rcParams['xtick.minor.visible']  = True\n", "mpl.rcParams['axes.facecolor'] = 'white'\n", "mpl.rcParams['grid.color'] = '0.8'\n", "mpl.rcParams['grid.alpha'] = '0.5'\n", "mpl.rcParams['figure.figsize'] = (8, 4)\n", "mpl.rcParams['figure.titlesize'] = 12.5\n", "mpl.rcParams['font.size'] = 12.5\n", "mpl.rcParams['legend.fontsize'] = 12.5\n", "mpl.rcParams['figure.dpi'] = 300\n", "mpl.rcParams['font.family'] = 'serif'\n", "mpl.rcParams['text.usetex'] = False\n", "\n", "\n", "sys.path.append('../')\n", "import interflow as itf\n", "import interflow.fabrics as fabrics\n", "import interflow.gmm as gmm\n", "import interflow.stochastic_interpolant as stochastic_interpolant\n", "\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "\n", "if torch.cuda.is_available():\n", "    device = 'cuda'\n", "    print('Using GPU.')\n", "else:\n", "    device = 'cpu'\n", "    print('Using the cpu. No GPU!')"]}, {"cell_type": "markdown", "id": "61f2a79f", "metadata": {}, "source": ["## Utility functions"]}, {"cell_type": "code", "execution_count": null, "id": "2223ec69", "metadata": {}, "outputs": [], "source": ["def setup_random_covs(N: int, d: int):\n", "    Cs = torch.zeros(N, d, d)\n", "    for ii in range(N):\n", "        C = torch.randn(d, d)\n", "        Cs[ii] = (C<PERSON>T @ C + 0.5*torch.eye(d))\n", "    \n", "    return Cs\n", "\n", "\n", "def setup_random_means(N: int, d: int, scale: float):\n", "    return scale*torch.randn((N, d))"]}, {"cell_type": "markdown", "id": "de01ab37", "metadata": {}, "source": ["## Setup interpolant"]}, {"cell_type": "code", "execution_count": null, "id": "70f85152", "metadata": {}, "outputs": [], "source": ["torch.manual_seed(12)\n", "\n", "d           = 2\n", "N0          = 1\n", "N1          = 3\n", "scale       = 3\n", "gamma_type  = 'brownian'\n", "path        = 'linear'\n", "p0s         = (torch.ones(N0) / N0).to(device)\n", "p1s         = (torch.ones(N1) / N1).to(device)\n", "mu0s        = setup_random_means(N0, d, scale).to(device)\n", "mu1s        = setup_random_means(N1, d, scale).to(device)\n", "C0s         = setup_random_covs(N0, d).to(device)\n", "C1s         = setup_random_covs(N1, d).to(device)\n", "\n", "interpolant = gmm.GMMInterpolant(\n", "    p0s, p1s, mu0s, mu1s, C0s, C1s, path, gamma_type, device\n", ")"]}, {"cell_type": "markdown", "id": "e3c10af8", "metadata": {}, "source": ["## Sample from base and target"]}, {"cell_type": "code", "execution_count": null, "id": "d8000f4b", "metadata": {}, "outputs": [], "source": ["nsamples = 10000\n", "fig, axs = plt.subplots(nrows=1, ncols=2, constrained_layout=True, figsize=(3,2))\n", "rho0_samples = interpolant.sample_rho0(nsamples)\n", "rho1_samples = interpolant.sample_rho1(nsamples)\n", "\n", "for ax in axs:\n", "    ax.set_aspect(1.0)\n", "\n", "axs[0].scatter(rho0_samples[:, 0].cpu().numpy(), \n", "               rho0_samples[:, 1].cpu().numpy(), s=0.25, alpha=0.25)\n", "axs[1].scatter(rho1_samples[:, 0].cpu().numpy(), \n", "               rho1_samples[:, 1].cpu().numpy(), s=0.25, alpha=0.25)\n", "\n", "axs[0].scatter(mu0s[:, 0].cpu().numpy(), \n", "               mu0s[:, 1].cpu().numpy(), color='red', marker='x')\n", "axs[1].scatter(mu1s[:, 0].cpu().numpy(), \n", "               mu1s[:, 1].cpu().numpy(), color='red', marker='x')"]}, {"cell_type": "markdown", "id": "77b9289e", "metadata": {}, "source": ["## Pushforward samples and compute likelihoods for SDE and ODE"]}, {"cell_type": "code", "execution_count": null, "id": "e126a8c5", "metadata": {}, "outputs": [], "source": ["n_save       = 5\n", "n_step       = 100\n", "n_likelihood = 5\n", "start_end    = (0.0001,0.9999)\n", "b, s         = interpolant.get_velocities()\n", "b, s         = fabrics.InputWrapper(b), fabrics.InputWrapper(s)\n", "epsilon      = torch.tensor(2.0).to(device)\n", "\n", "\n", "diff_interp  = stochastic_interpolant.Interpolant(path, gamma_type)\n", "\n", "\n", "sde_flow = stochastic_interpolant.SDEIntegrator(\n", "    b,\n", "    s,\n", "    epsilon,\n", "    diff_interp,\n", "    n_save,\n", "    start_end,\n", "    n_step,\n", "    n_likelihood\n", ")\n", "\n", "pflow = stochastic_interpolant.PFlowIntegrator(\n", "    b,\n", "    method='rk4',\n", "    interpolant=diff_interp,\n", "    n_step=100\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "47ff18cb", "metadata": {"tags": []}, "outputs": [], "source": ["bs = 10000\n", "\n", "\n", "with torch.no_grad():\n", "    x0s                          = interpolant.sample_rho0(bs).to(device)\n", "    x1_hats_sde                  = sde_flow.rollout_forward(x0s, method='euler')[-1]\n", "    x0_hats_sde, dlogps_sde      = sde_flow.rollout_likelihood(x1_hats_sde)\n", "    likelihoods_sde = torch.mean(\n", "        interpolant.log_rho0(x0_hats_sde.reshape(-1, d)).reshape(n_likelihood, -1), axis=0\n", "    ) - dlogps_sde\n", "\n", "\n", "    x1_hats_ode, likelihoods_ode = pflow.rollout(x0s)\n", "    x1_hats_ode = x1_hats_ode[-1]\n", "    likelihoods_ode = likelihoods_ode[-1]\n", "    likelihoods_ode += interpolant.log_rho0(x0s)"]}, {"cell_type": "code", "execution_count": null, "id": "ca592da2", "metadata": {}, "outputs": [], "source": ["nsamples = 10000\n", "fig, axs = plt.subplots(nrows=1, ncols=3, constrained_layout=True, sharex=True, sharey=True)\n", "axs[0].set_title('target')\n", "axs[1].set_title('SDE')\n", "axs[2].set_title('ODE')\n", "cmap = sns.color_palette(\"mako\", as_cmap=True)\n", "\n", "\n", "rho1_samples = interpolant.sample_rho1(nsamples)\n", "rho1_likes   = interpolant.log_rho1(rho1_samples)\n", "vmin         = 0.9*min([torch.min(rho1_likes), torch.min(likelihoods_sde), torch.min(likelihoods_ode)])\n", "vmax         = 0.9*max([torch.max(rho1_likes), torch.max(likelihoods_sde), torch.max(likelihoods_ode)])\n", "norm         = mpl.colors.Normalize(vmin, vmax)\n", "mappable     = mpl.cm.ScalarMappable(norm, cmap=cmap)\n", "\n", "\n", "axs[0].scatter(rho1_samples[:, 0].cpu().numpy(), \n", "               rho1_samples[:, 1].cpu().numpy(), \n", "               s=0.25, alpha=.5, c=rho1_likes.cpu().numpy(), cmap=cmap, norm=norm)\n", "\n", "\n", "axs[1].scatter(x1_hats_sde[:, 0].cpu().numpy(), \n", "               x1_hats_sde[:, 1].cpu().numpy(), \n", "               s=0.25, alpha=.5, c=likelihoods_sde.cpu().numpy(), cmap=cmap, norm=norm)\n", "\n", "\n", "axs[2].scatter(x1_hats_ode[:, 0].cpu().numpy(), \n", "               x1_hats_ode[:, 1].cpu().numpy(), \n", "               s=0.25, alpha=.5, c=likelihoods_ode.cpu().numpy(), cmap=cmap, norm=norm)\n", "\n", "\n", "cbar = fig.colorbar(mappable, ax=axs.ravel().tolist(), \n", "                    location='right',\n", "                    fraction=0.05,\n", "                    pad=0.025,\n", "                    shrink=0.35, \n", "                    extend='both')\n", "\n", "\n", "for ax in axs.ravel():\n", "    ax.set_aspect(1.0)"]}, {"cell_type": "markdown", "id": "697bc3db", "metadata": {}, "source": ["# 1d Visualizations"]}, {"cell_type": "markdown", "id": "4b4e222d", "metadata": {}, "source": ["## Linear Interpolant: <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "71e2696e", "metadata": {}, "outputs": [], "source": ["d           = 1\n", "N0          = 2\n", "N1          = 3\n", "epsilon     = torch.tensor(5.0).to(device)\n", "path        = 'linear'\n", "gamma_type  = 'brownian'\n", "p0s         = (torch.ones(N0) / N0).to(device)\n", "p1s         = (torch.ones(N1) / N1).to(device)\n", "mu0s        = torch.tensor([-3.0, 3.0]).reshape((N0, d)).to(device)\n", "C0s         = torch.tensor([0.25, 0.25]).reshape((N0, d, d)).to(device)\n", "mu1s        = torch.tensor([-6.0, 0.0, 6.0]).reshape((N1, d)).to(device)\n", "C1s         = torch.tensor([0.5, 0.33, 0.5]).reshape((N1, d, d)).to(device)\n", "\n", "gmm_interpolant      = gmm.GMMInterpolant(p0s, p1s, mu0s, mu1s, C0s, C1s, path, gamma_type, device)\n", "gmm_gam0_interpolant = gmm.GMMInterpolant(p0s, p1s, mu0s, mu1s, C0s, C1s, path,     'zero', device)"]}, {"cell_type": "code", "execution_count": null, "id": "9e6a6a4d", "metadata": {}, "outputs": [], "source": ["n_step         = 100\n", "n_save         = n_step\n", "n_likelihood   = 1\n", "start_end      = (0.0001, 0.9999)\n", "b, s           = gmm_interpolant.get_velocities()\n", "b, s           = fabrics.InputWrapper(b), fabrics.InputWrapper(s)\n", "b_det, s_det   = gmm_gam0_interpolant.get_velocities()\n", "b_det, s_det   = fabrics.InputWrapper(b_det), fabrics.InputWrapper(s_det)\n", "\n", "\n", "sde_interpolant = stochastic_interpolant.Interpolant(path, gamma_type)\n", "sde_flow    = stochastic_interpolant.SDEIntegrator(\n", "    b, s, epsilon, interpolant = sde_interpolant,\n", "    n_save=n_save, start_end=start_end, n_step=n_step, n_likelihood=n_likelihood\n", ")\n", "\n", "pflow_interpolant = stochastic_interpolant.Interpolant(path, gamma_type)\n", "pflow       = stochastic_interpolant.PFlowIntegrator(\n", "              b, method='rk4', interpolant=pflow_interpolant, \n", "              n_step=n_save\n", ")\n", "\n", "pflow_gam0_interpolant = stochastic_interpolant.Interpolant(path, 'zero')\n", "pflow_gam0        = stochastic_interpolant.PFlowIntegrator(\n", "                    b_det, method='rk4', interpolant=pflow_gam0_interpolant, n_step=n_save\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b559b21c", "metadata": {}, "outputs": [], "source": ["bs = int(5e3)\n", "\n", "\n", "with torch.no_grad():\n", "    x0s                 = gmm_gam0_interpolant.sample_rho0(bs).to(device)\n", "    x1_hats_sde         = sde_flow.rollout_forward(x0s)\n", "    x1_hats_ode, _      = pflow.rollout(x0s)\n", "    x1_hats_ode_det, _  = pflow_gam0.rollout(x0s)\n", "\n", "\n", "x1_hats_sde      = x1_hats_sde.squeeze().cpu().numpy()\n", "x1_hats_ode      = x1_hats_ode.squeeze().cpu().numpy()\n", "x1_hats_ode_det  = x1_hats_ode_det.squeeze().cpu().numpy()"]}, {"cell_type": "markdown", "id": "3c51a499", "metadata": {}, "source": ["#### Trajectories + distribution over time"]}, {"cell_type": "code", "execution_count": null, "id": "118fb468", "metadata": {}, "outputs": [], "source": ["plt.close('all')\n", "plot_skip    = 10\n", "bins         = 'sqrt'\n", "hist_index   = -1\n", "plot_index   = np.random.randint(bs)\n", "fig, axs     = plt.subplots(nrows=3, ncols=2, sharex='col', constrained_layout=True)\n", "t_plots      = np.linspace(0.0, 1.0, n_save)\n", "cmap_trajs   = sns.cubehelix_palette(n_colors=bs//plot_skip)\n", "cmap_dists   = sns.cubehelix_palette(n_colors=3)\n", "\n", "\n", "# grid for plotting\n", "xs        = torch.linspace(-10, 10, 150).to(device)\n", "rho0      = vmap(gmm_interpolant.eval_distribution, in_dims=(None, 0))(torch.tensor(0), xs).cpu().numpy()\n", "rho1      = vmap(gmm_interpolant.eval_distribution, in_dims=(None, 0))(torch.tensor(1), xs).cpu().numpy()\n", "fig.suptitle(r\"Linear Interpolant: $\\gamma=0$ vs. $\\gamma = \\sqrt{t(1-t)}$\")\n", "\n", "\n", "# SDE plots\n", "axs[0, 0].set_prop_cycle('color', cmap_trajs)\n", "axs[0, 0].set_ylabel(rf\"$\\gamma \\neq 0, \\epsilon = {epsilon}$\")\n", "_ = axs[0, 0].plot(t_plots, x1_hats_sde[:, ::plot_skip], lw=1, alpha=0.1)\n", "_ = axs[0, 0].plot(t_plots, x1_hats_sde[:, plot_index],  lw=1, color='white', alpha=1.0)\n", "_ = axs[0, 1].hist(x0s.cpu().numpy(), bins=bins, density=True, color=cmap_dists[1], alpha=0.75)\n", "_ = axs[0, 1].hist(x1_hats_sde[hist_index], bins=bins, density=True, color=cmap_dists[2], alpha=0.75)\n", "_ = axs[0, 1].plot(xs.cpu().numpy(), rho0, color=cmap_dists[1])\n", "_ = axs[0, 1].plot(xs.cpu().numpy(), rho1, color=cmap_dists[2])\n", "\n", "\n", "# ODE plots\n", "axs[1, 0].set_ylabel(r\"$\\gamma \\neq 0$, ODE\")\n", "axs[1, 0].set_prop_cycle('color', cmap_trajs)\n", "_ = axs[1, 0].plot(t_plots, x1_hats_ode[:, ::plot_skip], lw=1, alpha=0.1)\n", "_ = axs[1, 0].plot(t_plots, x1_hats_ode[:, plot_index],  lw=1, color='white', alpha=1.0)\n", "_ = axs[1, 1].hist(x0s.cpu().numpy(), bins=bins, density=True, color=cmap_dists[1], alpha=0.75)\n", "_ = axs[1, 1].hist(x1_hats_ode[hist_index], bins=bins, density=True, color=cmap_dists[2], alpha=0.75)\n", "_ = axs[1, 1].plot(xs.cpu().numpy(), rho0, color=cmap_dists[1])\n", "_ = axs[1, 1].plot(xs.cpu().numpy(), rho1, color=cmap_dists[2])\n", "\n", "\n", "# ODE plots: \\epsilon = 0\n", "axs[2, 0].set_ylabel(r\"$\\gamma = 0$, ODE\")\n", "axs[2, 0].set_prop_cycle('color', cmap_trajs)\n", "axs[2, 0].set_xlabel('time')\n", "_ = axs[2, 0].plot(t_plots, x1_hats_ode_det[:, ::plot_skip], lw=1, alpha=0.1)\n", "_ = axs[2, 0].plot(t_plots, x1_hats_ode_det[:, plot_index],  lw=1, color='white', alpha=1.0)\n", "_ = axs[2, 1].hist(   x0s.cpu().numpy(), bins=bins, density=True, color=cmap_dists[1], alpha=0.75)\n", "_ = axs[2, 1].hist(x1_hats_ode_det[hist_index], bins=bins, density=True, color=cmap_dists[2], alpha=0.75)\n", "_ = axs[2, 1].plot(xs.cpu().numpy(), rho0, color=cmap_dists[1])\n", "_ = axs[2, 1].plot(xs.cpu().numpy(), rho1, color=cmap_dists[2])"]}, {"cell_type": "markdown", "id": "58c17aaa", "metadata": {}, "source": ["#### Distributions over time"]}, {"cell_type": "code", "execution_count": null, "id": "87968a77", "metadata": {}, "outputs": [], "source": ["plt.close('all')\n", "plot_skip     = 10\n", "bins          = 100\n", "n_hists       = 5\n", "skip          = x1_hats_sde.shape[0] // (n_hists-1)\n", "hist_indices  = list(np.arange(x1_hats_sde.shape[0])[::skip]) + [x1_hats_sde.shape[0]-1]\n", "total_samples = [x1_hats_sde, x1_hats_ode, x1_hats_ode_det]\n", "nrows         = len(total_samples)\n", "ncols         = len(hist_indices)\n", "fig, axs      = plt.subplots(nrows=nrows, ncols=ncols, constrained_layout=True, sharex=True, sharey=True)\n", "t_plots       = np.linspace(0.0, 1.0, n_save)\n", "cmap_trajs    = sns.cubehelix_palette(n_colors=bs//plot_skip)\n", "cmap_dists    = sns.cubehelix_palette(n_colors=3)\n", "titles        = [rf\"$\\gamma \\neq 0, \\epsilon = {epsilon}$\", rf\"$\\gamma \\neq 0$, ODE\", r\"$\\gamma = 0$, ODE\"]\n", "\n", "dt = torch.tensor(1/n_step)\n", "xs = torch.linspace(-10, 10, 150).to(device)\n", "fig.suptitle(r\"Linear Interpolant: $\\gamma=0$ vs. $\\gamma = \\sqrt{t(1-t)}$\")\n", "for ii in range(nrows):\n", "    for jj in range(ncols):\n", "        hist_index = hist_indices[jj]\n", "        samples = total_samples[ii][hist_index]\n", "        t = hist_index*dt\n", "        \n", "        if hist_index == hist_indices[-1]:\n", "            t += dt\n", "        \n", "        if ii == 0:\n", "            axs[ii, jj].set_title(f'$t=${t:.02f}')\n", "        \n", "        if jj == 0:\n", "            axs[ii, jj].set_ylabel(titles[ii])\n", "            axs[ii, jj].set_yticks([])\n", "\n", "        if ii == nrows-1:\n", "            gmm_gam0_interpolant\n", "            rhot = vmap(gmm_gam0_interpolant.eval_distribution, in_dims=(None, 0))(t, xs).cpu().numpy()\n", "            # rhot = vmap(interpolant_det.eval_distribution, in_dims=(None, 0))(t, xs).cpu().numpy()\n", "        else:\n", "            rhot = vmap(gmm_interpolant.eval_distribution, in_dims=(None, 0))(t, xs).cpu().numpy()\n", "\n", "        _ = axs[ii, jj].hist(samples, bins=bins, density=True, color=cmap_dists[2], alpha=0.75)\n", "        _ = axs[ii, jj].plot(xs.cpu().numpy(), rhot, color=cmap_dists[2])"]}, {"cell_type": "markdown", "id": "ea344573", "metadata": {}, "source": ["## Gaussian Encoder-Decoder"]}, {"cell_type": "code", "execution_count": null, "id": "9a5d7723", "metadata": {}, "outputs": [], "source": ["d           = 1\n", "N0          = 2\n", "N1          = 3\n", "gamma_type  = 'brownian'\n", "path        = 'encoding-decoding'\n", "epsilon     = torch.tensor(5.0).to(device)\n", "p0s         = (torch.ones(N0) / N0).to(device)\n", "p1s         = (torch.ones(N1) / N1).to(device)\n", "mu0s        = torch.tensor([-3.0, 3.0]).reshape((N0, d)).to(device)\n", "C0s         = torch.tensor([0.25, 0.25]).reshape((N0, d, d)).to(device)\n", "mu1s        = torch.tensor([-6.0, 0.0, 6.0]).reshape((N1, d)).to(device)\n", "C1s         = torch.tensor([0.5, 0.33, 0.5]).reshape((N1, d, d)).to(device)\n", "interpolant = gmm.GMMInterpolant(p0s, p1s, mu0s, mu1s, C0s, C1s, path, gamma_type, device)"]}, {"cell_type": "code", "execution_count": null, "id": "b5902693", "metadata": {}, "outputs": [], "source": ["n_step         = 100\n", "n_save         = n_step\n", "n_likelihood   = 1\n", "start_end      = (0.0001, 0.9999)\n", "bs          = int(5e3)\n", "b, s        = interpolant.get_velocities()\n", "b, s        = fabrics.InputWrapper(b), fabrics.InputWrapper(s)\n", "sde_flow    = stochastic_interpolant.SDEIntegrator(\n", "    b, s, epsilon, interpolant=stochastic_interpolant.Interpolant(path, gamma_type), \n", "    n_save=n_save, start_end=start_end, n_step=n_step, n_likelihood=n_likelihood\n", ")\n", "\n", "pflow       = stochastic_interpolant.PFlowIntegrator(\n", "    b, method='rk4', interpolant=stochastic_interpolant.Interpolant(path, gamma_type), n_step=n_save\n", ")\n", "\n", "\n", "with torch.no_grad():\n", "    x0s                 = interpolant.sample_rho0(bs).to(device)\n", "    x1_hats_sde         = sde_flow.rollout_forward(x0s)\n", "    x1_hats_ode, _      = pflow.rollout(x0s)\n", "\n", "\n", "x1_hats_sde      = x1_hats_sde.squeeze().cpu().numpy()\n", "x1_hats_ode      = x1_hats_ode.squeeze().cpu().numpy()"]}, {"cell_type": "markdown", "id": "7c0e498e", "metadata": {}, "source": ["#### Trajectories + Distributions"]}, {"cell_type": "code", "execution_count": null, "id": "df8fce94", "metadata": {}, "outputs": [], "source": ["plt.close('all')\n", "plot_skip    = 10\n", "bins         = 'sqrt'\n", "hist_index   = -1\n", "plot_index   = np.random.randint(bs)\n", "fig, axs     = plt.subplots(nrows=2, ncols=2, sharex='col', constrained_layout=True)\n", "t_plots      = np.linspace(0.0, 1.0, n_save)\n", "cmap_trajs   = sns.cubehelix_palette(n_colors=bs//plot_skip)\n", "cmap_dists   = sns.cubehelix_palette(n_colors=3)\n", "\n", "\n", "# grid for plotting\n", "xs        = torch.linspace(-10, 10, 150).to(device)\n", "rho0      = vmap(interpolant.eval_distribution, in_dims=(None, 0))(torch.tensor(0), xs).cpu().numpy()\n", "rho1      = vmap(interpolant.eval_distribution, in_dims=(None, 0))(torch.tensor(1), xs).cpu().numpy()\n", "fig.suptitle('Gaussian Encoding-Decoding')\n", "\n", "\n", "# SDE plots\n", "axs[0, 0].set_prop_cycle('color', cmap_trajs)\n", "axs[0, 0].set_ylabel(rf\"$\\epsilon = {epsilon}$, SDE\")\n", "_ = axs[0, 0].plot(t_plots, x1_hats_sde[:, ::plot_skip], lw=1, alpha=0.1)\n", "_ = axs[0, 0].plot(t_plots, x1_hats_sde[:, plot_index],  lw=1, color='white', alpha=1.0)\n", "_ = axs[0, 1].hist(x0s.cpu().numpy(), bins=bins, density=True, color=cmap_dists[1], alpha=0.75)\n", "_ = axs[0, 1].hist(x1_hats_sde[hist_index], bins=bins, density=True, color=cmap_dists[2], alpha=0.75)\n", "_ = axs[0, 1].plot(xs.cpu().numpy(), rho0, color=cmap_dists[1])\n", "_ = axs[0, 1].plot(xs.cpu().numpy(), rho1, color=cmap_dists[2])\n", "\n", "\n", "# ODE plots\n", "axs[1, 0].set_ylabel(rf\"ODE\")\n", "axs[1, 0].set_prop_cycle('color', cmap_trajs)\n", "\n", "_ = axs[1, 0].plot(t_plots, x1_hats_ode[:, ::plot_skip], lw=1, alpha=0.1)\n", "_ = axs[1, 0].plot(t_plots, x1_hats_ode[:, plot_index],  lw=1, color='white', alpha=1.0)\n", "_ = axs[1, 1].hist(x0s.cpu().numpy(), bins=bins, density=True, color=cmap_dists[1], alpha=0.75)\n", "_ = axs[1, 1].hist(x1_hats_ode[hist_index], bins=bins, density=True, color=cmap_dists[2], alpha=0.75)\n", "_ = axs[1, 1].plot(xs.cpu().numpy(), rho0, color=cmap_dists[1])\n", "_ = axs[1, 1].plot(xs.cpu().numpy(), rho1, color=cmap_dists[2])"]}, {"cell_type": "markdown", "id": "8adb3466", "metadata": {}, "source": ["#### Distributions over time"]}, {"cell_type": "code", "execution_count": null, "id": "94f8748e", "metadata": {}, "outputs": [], "source": ["plt.close('all')\n", "plot_skip     = 10\n", "bins          = 100\n", "n_hists       = 5\n", "skip          = x1_hats_sde.shape[0] // (n_hists-1)\n", "hist_indices  = list(np.arange(x1_hats_sde.shape[0])[::skip]) + [x1_hats_sde.shape[0]-1]\n", "total_samples = [x1_hats_sde, x1_hats_ode]\n", "nrows         = len(total_samples)\n", "ncols         = len(hist_indices)\n", "fig, axs      = plt.subplots(nrows=nrows, ncols=ncols, constrained_layout=True, sharex=True, sharey=True)\n", "t_plots       = np.linspace(0.0, 1.0, n_save)\n", "cmap_trajs    = sns.cubehelix_palette(n_colors=bs//plot_skip)\n", "cmap_dists    = sns.cubehelix_palette(n_colors=3)\n", "titles        = [r\"$\\epsilon = 5.0$, SDE\", r\"ODE\"]\n", "\n", "\n", "xs = torch.linspace(-10, 10, 150).to(device)\n", "fig.suptitle('Gaussian Encoding-Decoding')\n", "for ii in range(nrows):\n", "    for jj in range(ncols):\n", "        hist_index = hist_indices[jj]\n", "        samples = total_samples[ii][hist_index]\n", "        t = hist_index*dt\n", "        \n", "        if hist_index == hist_indices[-1]:\n", "            t += dt\n", "        \n", "        if ii == 0:\n", "            axs[ii, jj].set_title(f'$t=${t:.02f}')\n", "        \n", "        if jj == 0:\n", "            axs[ii, jj].set_ylabel(titles[ii])\n", "            axs[ii, jj].set_yticks([])\n", "            \n", "        rhot = vmap(interpolant.eval_distribution, in_dims=(None, 0))(t, xs).cpu().numpy()\n", "\n", "        _ = axs[ii, jj].hist(samples, bins=bins, density=True, color=cmap_dists[2], alpha=0.75)\n", "        _ = axs[ii, jj].plot(xs.cpu().numpy(), rhot, color=cmap_dists[2])"]}, {"cell_type": "markdown", "id": "d82803c4", "metadata": {}, "source": ["# Paper Figures"]}, {"cell_type": "markdown", "id": "964a7201", "metadata": {}, "source": ["## Spatially Linear: Densities"]}, {"cell_type": "code", "execution_count": null, "id": "39c0f951", "metadata": {}, "outputs": [], "source": ["### Set up the interpolant.\n", "d          = 1\n", "N0         = 2\n", "N1         = 3\n", "p0s        = (torch.ones(N0) / N0).to(device)\n", "p1s        = (torch.ones(N1) / N1).to(device)\n", "mu0s       = torch.tensor([-3.0, 3.0]).reshape((N0, d)).to(device)\n", "C0s        = torch.tensor([0.25, 0.25]).reshape((N0, d, d)).to(device)\n", "mu1s       = torch.tensor([-6.0, 0.0, 6.0]).reshape((N1, d)).to(device)\n", "C1s        = torch.tensor([0.5, 0.33, 0.5]).reshape((N1, d, d)).to(device)\n", "gamma_type = 'brownian'\n", "\n", "\n", "og = gmm.GMMInterpolant(p0s, p1s, mu0s, mu1s, C0s, C1s, path='trig', gamma_type=gamma_type, device=device)\n", "trig = gmm.GMMInterpolant(p0s, p1s, mu0s, mu1s, C0s, C1s, path='trig', gamma_type=gamma_type, device=device)\n", "encode_decode = gmm.GMMInterpolant(p0s, p1s, mu0s, mu1s, C0s, C1s, path='encoding-decoding', gamma_type=gamma_type, device=device)"]}, {"cell_type": "code", "execution_count": null, "id": "ea5e37d6", "metadata": {}, "outputs": [], "source": ["plt.close('all')\n", "t_plots    = [0.0, 0.25, 0.5, 0.75, 1.0]\n", "t_plots    = [torch.tensor(t_plot) for t_plot in t_plots]\n", "gammas     = [r\"$\\gamma(t)=0$\", r\"$\\gamma(t) = \\sqrt{t(1-t)}$\", r\"$\\gamma(t) = \\sin^2(\\pi t)$\"]\n", "interps    = [og, trig, encode_decode]\n", "nrows      = len(interps)\n", "ncols      = len(t_plots)\n", "fig, axs   = plt.subplots(nrows=nrows, ncols=ncols, constrained_layout=True, sharex=True, sharey=True)\n", "cmap_dists = sns.cubehelix_palette(n_colors=5)\n", "color      = cmap_dists[4]\n", "fontsize   = 8\n", "rhosize    = 15\n", "\n", "\n", "xs = torch.linspace(-10, 10, 150)\n", "for ii in range(nrows):\n", "    interp = interps[ii]\n", "    for jj in range(ncols):\n", "        t = t_plots[jj]\n", "        axs[ii, jj].set_facecolor('white')\n", "        axs[ii, jj].grid(color='0.9', alpha=0.5)\n", "        axs[ii, jj].tick_params(which='both', direction='in', size=1.0)\n", "        axs[ii, jj].set_ylim([0, 1.0])\n", "\n", "\n", "        # time labels\n", "        if ii == 0:\n", "            axs[ii, jj].set_title(f'$t=${t:.02f}')\n", "\n", "\n", "        # \\rho_0 and \\rho_1 labels\n", "        if ii == 1 and jj == 0:\n", "            axs[ii, jj].text(0.1, 0.825, r\"$\\rho_0$\", size=rhosize, transform=axs[ii, jj].transAxes)\n", "        if ii == 1 and jj == (ncols-1):\n", "            axs[ii, jj].text(0.1, 0.825, r\"$\\rho_1$\", size=rhosize, transform=axs[ii, jj].transAxes)\n", "\n", "\n", "        # make plot\n", "        if (ii == 0 and (jj == 0 or jj == ncols-1)) or (ii == nrows-1 and (jj == 0 or jj == ncols-1)):\n", "            axs[ii, jj].axis('off')\n", "        else:\n", "            t = t.to(device)\n", "            xs = xs.to(device)\n", "            \n", "            rhot = vmap(interp.eval_distribution, in_dims=(None, 0))(t, xs).cpu().numpy()\n", "            _    = axs[ii, jj].plot(xs.cpu().numpy(), rhot, lw=2, color=color)\n", "            axs[ii, jj].fill_between(xs.cpu().numpy(), torch.zeros_like(xs).cpu().numpy(), rhot, color=color, alpha=0.75)\n", "            axs[ii, jj].tick_params(labelleft=False, labelbottom=False)"]}, {"cell_type": "markdown", "id": "156aeb3e-e75e-42b9-b4a6-bd11dce1e7e0", "metadata": {}, "source": ["### Trajectories"]}, {"cell_type": "code", "execution_count": null, "id": "f22496d7-1928-4ac0-a335-b3535caee039", "metadata": {}, "outputs": [], "source": ["### set up the GMM parameters\n", "d     = 1\n", "N0    = 2\n", "N1    = 3\n", "p0s   = (torch.ones(N0) / N0).to(device)\n", "p1s   = (torch.ones(N1) / N1).to(device)\n", "mu0s  = torch.tensor([-3.0, 3.0]).reshape((N0, d)).to(device)\n", "C0s   = torch.tensor([0.25, 0.25]).reshape((N0, d, d)).to(device)\n", "mu1s  = torch.tensor([-6.0, 0.0, 6.0]).reshape((N1, d)).to(device)\n", "C1s   = torch.tensor([0.5, 0.33, 0.5]).reshape((N1, d, d)).to(device)\n", "\n", "\n", "### set up the interpolants\n", "og = gmm.GMMInterpolant(\n", "    p0s, p1s, mu0s, mu1s, C0s, C1s, path='trig', \n", "    gamma_type='zero', device=device\n", ")\n", "\n", "\n", "trig = gmm.GMMInterpolant(\n", "    p0s, p1s, mu0s, mu1s, C0s, C1s, path='trig', \n", "    gamma_type='brownian', device=device\n", ")\n", "\n", "\n", "handshake = gmm.GMMInterpolant(\n", "    p0s, p1s, mu0s, mu1s, C0s, C1s, path='encoding-decoding', \n", "    gamma_type='brownian', device=device\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fb66550a-4393-49e1-9cbb-fec1bc58fd96", "metadata": {}, "outputs": [], "source": ["### compute the trajectories\n", "ntrajs       = 2500\n", "interps      = [og, trig, encode_decode]\n", "keys         = ['og', 'trig', 'encode_decode']\n", "eps_vals     = [torch.tensor(0.0), torch.tensor(0.5), torch.tensor(5.0)]\n", "# dt           = torch.tensor(5e-3)\n", "n_step       = 200\n", "n_save       = n_step\n", "n_likelihood = 1\n", "start_end    = (0,1)\n", "\n", "rslts = {}\n", "with torch.no_grad():\n", "    x0s = og.sample_rho0(ntrajs).to(device)\n", "\n", "    for ii, interp in enumerate(interps):\n", "        rslts[keys[ii]] = {}\n", "\n", "        for jj, eps in enumerate(eps_vals):\n", "            b, s = interp.get_velocities()\n", "            b, s = fabrics.InputWrapper(b).to(device), fabrics.InputWrapper(s).to(device)\n", "            if eps > 0:\n", "                sde_flow = stochastic_interpolant.SDEIntegrator(b, s, eps, \n", "                                                                interpolant=interp, \n", "                                                                n_save=n_save, \n", "                                                                start_end = start_end, \n", "                                                                n_step=n_step, \n", "                                                                n_likelihood=n_likelihood)\n", "                x1s = sde_flow.rollout_forward(x0s, method='heun')\n", "            else:\n", "                \n", "                pflow = stochastic_interpolant.PFlowIntegrator(b, method='rk4', interpolant=interp, n_step=n_save)\n", "                x1s, _ = pflow.rollout(x0s)\n", "\n", "            rslts[keys[ii]][f'eps={eps}'] = x1s\n", "            print(f'Finished epsilon={eps} for {keys[ii]}')"]}, {"cell_type": "code", "execution_count": null, "id": "10a6c62f-b7a8-4b4a-b0da-0d71afad5752", "metadata": {}, "outputs": [], "source": ["plt.close('all')\n", "nrows     = len(eps_vals)\n", "ncols     = len(interps)\n", "labels    = [r\"trig: $\\gamma(t)=0$\", \n", "             r\"trig: $\\gamma(t) = \\sqrt{t(1-t)}$\", \n", "             r\"encoder: $\\gamma(t) = \\sqrt{t(1-t)}$\"]\n", "fig, axs  = plt.subplots(nrows=nrows, ncols=ncols, constrained_layout=True, sharex=True, sharey=True)\n", "cmap      = sns.cubehelix_palette(n_colors=ntrajs)\n", "t_plots   = np.linspace(0.0, 1.0, n_save)\n", "n_visible = 3\n", "plot_inds = np.random.randint(ntrajs, size=n_visible)\n", "\n", "\n", "for ii in range(nrows):\n", "    for jj in range(ncols):\n", "        eps = eps_vals[ii]\n", "        axs[ii, jj].set_prop_cycle('color', cmap)\n", "        axs[ii, jj].set_facecolor('white')\n", "        axs[ii, jj].grid(color='0.9', alpha=0.5)\n", "        axs[ii, jj].tick_params(which='both', direction='in', size=1.0)\n", "        axs[ii, jj].set_xlim([0.0, 1.0])\n", "        axs[ii, jj].set_xticks([0, 0.25, 0.5, 0.75, 1.0])\n", "        axs[ii, jj].tick_params(labelleft=False)\n", "\n", "\n", "        # labels\n", "        if jj == 0:\n", "            axs[ii, jj].set_ylabel(rf\"$\\epsilon={eps}$\")\n", "        if ii == 0:\n", "            axs[ii, jj].set_title(labels[jj])\n", "        if ii == nrows-1:\n", "            axs[ii, jj].set_xlabel('time')\n", "\n", "                \n", "        x1s = rslts[keys[jj]][f'eps={eps}']\n", "        axs[ii, jj].plot(t_plots,         x1s[:, :, 0].cpu().data.numpy(), lw=1.0, alpha=0.15, rasterized=True)\n", "        axs[ii, jj].plot(t_plots, x1s[:, plot_inds, 0].cpu().data.numpy(), lw=1.0, color='white')"]}], "metadata": {"kernelspec": {"display_name": "afm", "language": "python", "name": "afm"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}