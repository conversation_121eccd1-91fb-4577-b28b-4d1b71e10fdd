{"cells": [{"cell_type": "code", "execution_count": null, "id": "f55eb47d", "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.distributions as D\n", "from torchdiffeq import odeint_adjoint as odeint\n", "import numpy as np\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "import math\n", "import time\n", "import sys\n", "sys.path.append('../')\n", "from typing import Tuple, Any\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "\n", "import interflow as itf\n", "import interflow.prior as prior\n", "import interflow.fabrics\n", "import interflow.stochastic_interpolant as stochastic_interpolant\n", "from torch import autograd\n", "from functorch import jacfwd, vmap\n", "\n", "\n", "if torch.cuda.is_available():\n", "    print('CUDA available, setting default tensor residence to GPU.')\n", "    itf.util.set_torch_device('cuda')\n", "else:\n", "    print('No CUDA device found!')\n", "print(itf.util.get_torch_device())\n", "\n", "\n", "print(\"Torch version:\", torch.__version__)"]}, {"cell_type": "markdown", "id": "a6f48119", "metadata": {}, "source": ["## Utility functions"]}, {"cell_type": "code", "execution_count": null, "id": "3330e609", "metadata": {}, "outputs": [], "source": ["def grab(var):\n", "    \"\"\"Take a tensor off the gpu and convert it to a numpy array on the CPU.\"\"\"\n", "    return var.detach().cpu().numpy()\n", "\n", "\n", "def compute_likelihoods(\n", "    b: <PERSON>.nn.<PERSON>,\n", "    s: <PERSON>.nn.<PERSON>,\n", "    interpolant: stochastic_interpolant.Interpolant,\n", "    n_save: int,\n", "    n_step: int,\n", "    eps: int,\n", "    bs: int\n", ") -> <PERSON><PERSON>[torch.tensor, torch.tensor]:\n", "    \"\"\"Draw samples from the probability flow and SDE models, and compute likelihoods.\"\"\"\n", "    \n", "    \n", "    sde_flow = stochastic_interpolant.SDEIntegrator(\n", "        b=b, s=s, eps=eps, interpolant=interpolant, n_save=n_save, n_likelihood=1, n_step=n_step\n", "    )\n", "    pflow = stochastic_interpolant.PFlowIntegrator(b=b,  \n", "                                                  method='dopri5', \n", "                                                  interpolant=interpolant,\n", "                                                  n_step=3)\n", "    \n", "    with torch.no_grad():\n", "        x0_tests  = base(bs)\n", "        xfs_sde   = sde_flow.rollout_forward(x0_tests) # [n_save x bs x dim]\n", "        xf_sde    = grab(xfs_sde[-1].squeeze())        # [bs x dim]\n", "        # ([1, bs, dim], [bs])\n", "        x0s_sdeflow, _ = sde_flow.rollout_likelihood(xfs_sde[-1])\n", "    \n", "\n", "    logp0                  = base.log_prob(x0_tests)            # [bs]\n", "    xfs_pflow, dlogp_pflow = pflow.rollout(x0_tests)            # [n_save x bs x dim], [n_save x bs]\n", "    logpx_pflow            = logp0 + dlogp_pflow[-1].squeeze()  # [bs]\n", "    xf_pflow               = grab(xfs_pflow[-1].squeeze())      # [bs x dim]\n", "\n", "\n", "    return xf_sde, xf_pflow, logpx_pflow\n", "\n", "\n", "def log_metrics(\n", "    b: <PERSON>.nn.<PERSON>,\n", "    s: <PERSON>.nn.<PERSON>,\n", "    interpolant: stochastic_interpolant.Interpolant,\n", "    n_save: int,\n", "    n_step: int,\n", "    likelihood_bs: int, \n", "    b_loss: torch.tensor,\n", "    s_loss: torch.tensor,\n", "    loss: torch.tensor,\n", "    b_grad: torch.tensor,\n", "    s_grad: torch.tensor,\n", "    eps: torch.tensor,\n", "    data_dict: dict\n", ") -> None:\n", "    # log loss and gradient data\n", "    b_loss   = grab(b_loss).mean(); data_dict['b_losses'].append(b_loss)\n", "    s_loss   = grab(s_loss).mean(); data_dict['s_losses'].append(s_loss)\n", "    loss     = grab(loss).mean(); data_dict['losses'].append(loss)\n", "    b_grad   = grab(b_grad).mean(); data_dict['b_grads'].append(b_grad)\n", "    s_grad   = grab(s_grad).mean(); data_dict['s_grads'].append(s_grad)\n", "\n", "    \n", "    # compute and log likelihood data\n", "    _, _, logpx_pflow = compute_likelihoods(\n", "        b, s, interpolant, n_save, n_step, eps, likelihood_bs)\n", "    \n", "    logpx_pflow = grab(logpx_pflow).mean(); data_dict['logps_pflow'].append(logpx_pflow)\n", "    \n", "    \n", "def make_plots(\n", "    b: <PERSON>.nn.<PERSON>,\n", "    s: <PERSON>.nn.<PERSON>,\n", "    interpolant: stochastic_interpolant.Interpolant,\n", "    n_save: int,\n", "    n_step: int, ## number of sde steps in [0,1]\n", "    likelihood_bs: int,\n", "    counter: int,\n", "    metrics_freq: int,\n", "    eps: torch.tensor,\n", "    data_dict: dict\n", ") -> None:\n", "    \"\"\"Make plots to visualize samples and evolution of the likelihood.\"\"\"\n", "    # compute likelihood and samples for SDE and probability flow.\n", "    xf_sde, xf_pflow, logpx_pflow = compute_likelihoods(\n", "        b, s, interpolant, n_save, n_step, eps, likelihood_bs\n", "    )\n", "\n", "\n", "    ### plot the loss, test logp, and samples from interpolant flow\n", "    fig, axes = plt.subplots(1,4, figsize=(16,4))\n", "    print(\"EPOCH:\", counter)\n", "    print(\"LOSS, GRAD:\", loss, b_grad, s_grad)\n", "\n", "\n", "    # plot loss over time.\n", "    nsaves = len(data_dict['losses'])\n", "    epochs = np.arange(nsaves)*metrics_freq\n", "    axes[0].plot(epochs, data_dict['losses'], label=\" b + s\")\n", "    axes[0].plot(epochs, data_dict['b_losses'], label=\"b\")\n", "    axes[0].plot(epochs, data_dict['s_losses'], label = \"s\" )\n", "    axes[0].set_title(\"LOSS\")\n", "    axes[0].legend()\n", "\n", "\n", "    # plot samples from SDE.\n", "    axes[1].scatter(\n", "        xf_sde[:,0], xf_sde[:,1], vmin=0.0, vmax=0.05, alpha = 0.2)\n", "    axes[1].set_xlim(-5,5)\n", "    axes[1].set_ylim(-6.5,6.5)\n", "    axes[1].set_title(\"Samples from SDE\", fontsize=14)\n", "\n", "\n", "    # plot samples from pflow\n", "    axes[2].scatter(\n", "        xf_pflow[:,0], xf_pflow[:,1], vmin=0.0, vmax=0.05, alpha = 0.2, c=grab(torch.exp(logpx_pflow).detach()))\n", "    axes[2].set_xlim(-5,5)\n", "    axes[2].set_ylim(-6.5,6.5)\n", "    axes[2].set_title(\"Samples from P<PERSON>low\", fontsize=14)\n", "\n", "\n", "    # plot likelihood estimates.\n", "    axes[3].plot(epochs, data_dict['logps_pflow'],   label='pflow', color='purple')\n", "    axes[3].set_title(r\"$\\log p$ from PFlow\")\n", "    axes[3].legend(loc='best')\n", "    axes[3].set_ylim(-7,0)\n", "\n", "\n", "    fig.suptitle(r\"$\\epsilon = $\" + str(grab(eps)),  fontsize=16, y = 1.05)\n", "    plt.show()\n", "    \n", "    \n", "\n", "\n", "def train_step(\n", "    bs: int,\n", "    interpolant: stochastic_interpolant.Interpolant,\n", "    opt_b  : Any,\n", "    opt_s  : Any,\n", "    sched_b: Any, \n", "    sched_s: Any,\n", "):\n", "    \"\"\"\n", "    Take a single step of optimization on the training set.\n", "    \"\"\"\n", "    opt_b.zero_grad()\n", "    opt_s.zero_grad()\n", "\n", "\n", "    # construct batch\n", "    x0s = base(bs)\n", "    x1s = target(bs)\n", "    ts  = torch.rand(size=(bs,))\n", "\n", "\n", "    # compute the loss\n", "    loss_start = time.perf_counter()\n", "    loss_b     = loss_fn_b(b, x0s, x1s, ts, interpolant)\n", "    loss_s     = loss_fn_s(s, x0s, x1s, ts, interpolant)\n", "    loss_val   = loss_b + loss_s\n", "    loss_end   = time.perf_counter()\n", "\n", "\n", "    # compute the gradient\n", "    backprop_start = time.perf_counter()\n", "    loss_b.backward()\n", "    loss_s.backward()\n", "    b_grad = torch.tensor([torch.nn.utils.clip_grad_norm_(b.parameters(), float('inf'))])\n", "    s_grad = torch.tensor([torch.nn.utils.clip_grad_norm_(s.parameters(), float('inf'))])\n", "    backprop_end = time.perf_counter()\n", "\n", "\n", "    # perform the update.\n", "    update_start = time.perf_counter()\n", "    opt_b.step()\n", "    opt_s.step()\n", "    sched_b.step()\n", "    sched_s.step()\n", "    update_end = time.perf_counter()\n", "\n", "\n", "    if counter < 5:\n", "        print(f'[Loss: {loss_end - loss_start}], [Backprop: {backprop_end-backprop_start}], [Update: {update_end-update_start}].')\n", "\n", "\n", "    return loss_val.detach(), loss_b.detach(), loss_s.detach(), b_grad.detach(), s_grad.detach()"]}, {"cell_type": "code", "execution_count": null, "id": "2670e713-e83c-4ec7-84f3-3dd4f0912f2e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "af7ce2eb", "metadata": {}, "source": ["### Define target"]}, {"cell_type": "code", "execution_count": null, "id": "03ff28bd", "metadata": {}, "outputs": [], "source": ["\n", "ndim = 2\n", "def target(bs):\n", "    x1 = torch.rand(bs) * 4 - 2\n", "    x2_ = torch.rand(bs) - torch.randint(2, (bs,)) * 2\n", "    x2 = x2_ + (torch.floor(x1) % 2)\n", "    return (torch.cat([x1[:, None], x2[:, None]], 1) * 2)\n", "\n", "\n", "target_samples = grab(target(10000))\n", "\n", "\n", "fig = plt.figure(figsize=(6,6))\n", "plt.hist2d(target_samples[:,0], target_samples[:,1], bins = 100, range=[[-4,4],[-4,4]]);\n", "plt.title(\"Checker Target\")\n", "plt.show()\n", "\n", "\n", "print(\"Batch Shape:\", target_samples.shape)\n", "# target_logp_est = target.log_prob(target(10000)).mean()"]}, {"cell_type": "markdown", "id": "48a47a52", "metadata": {}, "source": ["### Define Base Distribution"]}, {"cell_type": "code", "execution_count": null, "id": "27190b59", "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "Weights  = torch.tensor\n", "Covs     = torch.tensor\n", "Means    = torch.tensor\n", "\n", "@dataclass\n", "class GMM():\n", "    lams:  Weights   # [N]       GMM weights\n", "    mus :  Means     # [N, d]    GMM means\n", "    Cs  :  Covs      # [N, d, d] GMM covariances\n", "    \n", "    \n", "    def __post_init__(self):\n", "        # extract dimensions\n", "        self.N = self.lams.shape[0]\n", "        self.d  = self.Cs.shape[1]\n", "        \n", "        # ensure GMM weights are normalized\n", "        assert(torch.allclose(torch.sum(self.lams),torch.ones(1), 1e-4))\n", "        self.rho = D.MixtureSameFamily(\n", "                   <PERSON><PERSON>Catego<PERSON>(self.lams),\n", "                   <PERSON><PERSON>(self.mus, self.Cs)\n", "                   )\n", "            \n", "    def single_logp(self, x, mu, sigma):\n", "        \"\"\"\n", "        -logp(x | mu, sigma) = 0.5 * log(2π) + log(σ) + 0.5 * ((x - μ)/σ)^2\n", "        \"\"\"\n", "\n", "        mahal_dist = torch.einsum('ki,ij,kj->k', (x-mu), torch.linalg.inv(sigma), (x-mu))\n", "        return -0.5 * (self.d* torch.log(torch.tensor(2) * torch.pi) + torch.linalg.slogdet(sigma)[-1] + mahal_dist)\n", "             \n", "    def log_prob(self, x):\n", "        \"\"\"\n", "        log p, summed for all mixture modes\n", "        \"\"\"\n", "        assert len(self.mus) == len(self.Cs)\n", "        log_pdfs = torch.empty((self.N, x.shape[0]))\n", "        for i in range(self.N):\n", "            log_pdfs[i] = torch.log(self.lams[i]) + self.single_logp(x, self.mus[i], self.Cs[i])\n", "        return torch.logsumexp(log_pdfs, dim=0)\n", "    \n", "    def sample(self, bs: int):\n", "        return self.rho.sample((bs,))\n", "    \n", "    def __call__(self, bs: int):\n", "        return self.sample(bs)"]}, {"cell_type": "code", "execution_count": null, "id": "dad584b0-90da-40ee-ac96-bc30cb19ed74", "metadata": {}, "outputs": [], "source": ["torch.manual_seed(4)\n", "\n", "d    = 2 # dimension\n", "N    = 3 # number of components in the target gaussian mixture\n", "lams = torch.tensor(1/N)*torch.ones(N) # uniform weights on the gaussian mixture\n", "mus1 = 2.0*torch.randn(N,d) # mean vectors of the gaussian mixture components\n", "Cs1 = torch.zeros(N, d, d)  # covariances of the gaussian mixture components\n", "\n", "## fill in with random values\n", "scale_fac = 1.0\n", "for ii in range(N):\n", "    C = torch.randn(d, d)\n", "    Cs1[ii] = (C.T @ C / scale_fac + torch.eye(d))/4.0\n", "\n", "base = GMM(lams, mus1, Cs1)\n", "base_samples = grab(base(20000))"]}, {"cell_type": "code", "execution_count": null, "id": "90eccf1e", "metadata": {}, "outputs": [], "source": ["fig = plt.figure(figsize=(6,6,))\n", "plt.scatter(base_samples[:,0], base_samples[:,1],  label = 'base', alpha = 0.2);\n", "plt.scatter(target_samples[:,0], target_samples[:,1], alpha = 0.2);\n", "plt.title(\"Bimodal Target\")\n", "plt.title(\"Base vs Target\")\n", "plt.show()"]}, {"cell_type": "markdown", "id": "13e4caad", "metadata": {}, "source": ["### Define Interpolant: A two sided interpolant, meaning that $x_0$ does not just come from standard Gaussian.\n", "\n", "Here we use a Gaussian Mixture as example, though it could be anything."]}, {"cell_type": "code", "execution_count": null, "id": "3bb13153", "metadata": {}, "outputs": [], "source": ["gamma_type = 'brownian'\n", "path = 'encoding-decoding'\n", "interpolant  = stochastic_interpolant.Interpolant(path=path, gamma_type=gamma_type)"]}, {"cell_type": "markdown", "id": "e5c00b60-2f61-4b6f-8a93-3a2099ed6535", "metadata": {}, "source": ["### Define losses for b and s"]}, {"cell_type": "code", "execution_count": null, "id": "ceb6afef-c2d2-4f96-979f-642cff8a7922", "metadata": {}, "outputs": [], "source": ["loss_fn_b = stochastic_interpolant.make_loss(method='shared', interpolant = interpolant, loss_type='b')\n", "loss_fn_s = stochastic_interpolant.make_loss(method='shared', interpolant = interpolant, loss_type='s')"]}, {"cell_type": "markdown", "id": "8df9c81d", "metadata": {}, "source": ["### Define velocity field and optimizers"]}, {"cell_type": "code", "execution_count": null, "id": "dba805f8", "metadata": {}, "outputs": [], "source": ["base_lr      = 2e-3\n", "hidden_sizes = [256, 256, 256, 256]\n", "in_size      = (ndim+1)\n", "out_size     = (ndim)\n", "inner_act    = 'relu'\n", "final_act    = 'none'\n", "print_model  = False\n", "\n", "\n", "b       = itf.fabrics.make_fc_net(hidden_sizes=hidden_sizes, in_size=in_size, out_size=out_size, inner_act=inner_act, final_act=final_act)\n", "s       = itf.fabrics.make_fc_net(hidden_sizes=hidden_sizes, in_size=in_size, out_size=out_size, inner_act=inner_act, final_act=final_act)\n", "opt_b   = torch.optim.Adam(b.parameters(), lr=base_lr)\n", "opt_s   = torch.optim.Adam(s.parameters(), lr=base_lr)\n", "sched_b = torch.optim.lr_scheduler.StepLR(optimizer=opt_b, step_size=1500, gamma=0.4)\n", "sched_s = torch.optim.lr_scheduler.StepLR(optimizer=opt_s, step_size=1500, gamma=0.4)\n", "\n", "\n", "eps          = torch.tensor(0.5)\n", "N_era        = 14\n", "N_epoch      = 500\n", "plot_bs      = 5000  # number of samples to use when plotting\n", "bs           = 2000    # number of samples from rho_0 in batch\n", "metrics_freq = 50    # how often to log metrics, e.g. if logp is not super cheap don't do it everytime\n", "plot_freq    = 500   # how often to plot\n", "n_save       = 10    # how often to checkpoint SDE integrator\n", "loss_fac     = 4.0   # ratio of learning rates for w to v\n", "n_step       = 100   # number of steps taken by the SDE in [0,1]\n", "\n", "\n", "if print_model:\n", "    print(\"Here's the model b, s:\", b, s)"]}, {"cell_type": "code", "execution_count": null, "id": "18f33113", "metadata": {}, "outputs": [], "source": ["data_dict = {\n", "    'losses': [],\n", "    'b_losses': [],\n", "    's_losses': [],\n", "    'b_grads': [],\n", "    's_grads': [],\n", "    'times': [],\n", "    'logps_pflow': [],\n", "}\n", "\n", "counter = 1\n", "for i, era in enumerate(range(N_era)):\n", "    for j, epoch in enumerate(range(N_epoch)):\n", "        loss, b_loss, s_loss, b_grad, s_grad = train_step(bs, interpolant, opt_b, opt_s, sched_b, sched_s,\n", "        )\n", "\n", "\n", "        if (counter - 1) % metrics_freq == 0:\n", "            log_metrics(b, s, interpolant, n_save, n_step, bs, b_loss, \n", "                        s_loss, loss, b_grad, s_grad, eps, data_dict)\n", "\n", "\n", "        if (counter - 1) % plot_freq == 0:\n", "            make_plots(b, s, interpolant, n_save, n_step, plot_bs, counter, metrics_freq, eps, data_dict)\n", "\n", "\n", "        counter+=1"]}, {"cell_type": "code", "execution_count": null, "id": "e58ad807-f71b-4d39-8f13-85fedd706631", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "afm", "language": "python", "name": "afm"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}